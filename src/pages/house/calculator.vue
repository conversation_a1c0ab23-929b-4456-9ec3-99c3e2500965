<template>
  <view class="calculator-page">
    <!-- 导航栏 -->
    <uni-nav-bar
      :fixed="true"
      :border="false"
      background-color="transparent"
      color="#333"
      title="房贷计算器"
      left-icon="left"
      @clickLeft="goBack"
    />

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 计算器表单 -->
      <view class="calculator-form bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
        <view class="px-30rpx py-30rpx">
          <view class="section-title mb-30rpx">
            <text class="text-30rpx font-bold">贷款信息</text>
          </view>

          <!-- 房屋总价 -->
          <view class="form-group mb-30rpx">
            <view class="form-label mb-16rpx">
              <text class="text-28rpx font-medium">房屋总价</text>
            </view>
            <view class="input-wrapper">
              <input
                v-model="formData.totalPrice"
                type="number"
                placeholder="请输入房屋总价"
                class="form-input"
              />
              <text class="input-unit">万元</text>
            </view>
          </view>

          <!-- 首付比例 -->
          <view class="form-group mb-30rpx">
            <view class="form-label mb-16rpx">
              <text class="text-28rpx font-medium">首付比例</text>
            </view>
            <view class="ratio-options flex">
              <view
                v-for="ratio in downPaymentRatios"
                :key="ratio"
                class="ratio-item"
                :class="{ 'active': formData.downPaymentRatio === ratio }"
                @tap="selectDownPaymentRatio(ratio)"
              >
                <text>{{ ratio }}%</text>
              </view>
            </view>
          </view>

          <!-- 贷款年限 -->
          <view class="form-group mb-30rpx">
            <view class="form-label mb-16rpx">
              <text class="text-28rpx font-medium">贷款年限</text>
            </view>
            <view class="year-options flex">
              <view
                v-for="year in loanYears"
                :key="year"
                class="year-item"
                :class="{ 'active': formData.loanYear === year }"
                @tap="selectLoanYear(year)"
              >
                <text>{{ year }}年</text>
              </view>
            </view>
          </view>

          <!-- 贷款利率 -->
          <view class="form-group mb-30rpx">
            <view class="form-label mb-16rpx">
              <text class="text-28rpx font-medium">贷款利率</text>
            </view>
            <view class="input-wrapper">
              <input
                v-model="formData.interestRate"
                type="digit"
                placeholder="请输入年利率"
                class="form-input"
              />
              <text class="input-unit">%</text>
            </view>
          </view>

          <!-- 还款方式 -->
          <view class="form-group mb-30rpx">
            <view class="form-label mb-16rpx">
              <text class="text-28rpx font-medium">还款方式</text>
            </view>
            <view class="payment-methods flex">
              <view
                v-for="method in paymentMethods"
                :key="method.value"
                class="method-item"
                :class="{ 'active': formData.paymentMethod === method.value }"
                @tap="selectPaymentMethod(method.value)"
              >
                <text>{{ method.label }}</text>
              </view>
            </view>
          </view>

          <!-- 计算按钮 -->
          <view class="calculate-btn" @tap="calculateLoan">
            <text class="btn-text">开始计算</text>
          </view>
        </view>
      </view>

      <!-- 计算结果 -->
      <view v-if="result" class="result-section bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
        <view class="px-30rpx py-30rpx">
          <view class="section-title mb-30rpx">
            <text class="text-30rpx font-bold">计算结果</text>
          </view>

          <!-- 关键数据 -->
          <view class="key-data grid grid-cols-2 gap-20rpx mb-30rpx">
            <view class="data-item">
              <text class="data-label">贷款总额</text>
              <text class="data-value color-primary">{{ result.loanAmount }}万</text>
            </view>
            <view class="data-item">
              <text class="data-label">月供金额</text>
              <text class="data-value color-red">{{ result.monthlyPayment }}元</text>
            </view>
            <view class="data-item">
              <text class="data-label">支付利息</text>
              <text class="data-value color-orange">{{ result.totalInterest }}万</text>
            </view>
            <view class="data-item">
              <text class="data-label">还款总额</text>
              <text class="data-value color-blue">{{ result.totalPayment }}万</text>
            </view>
          </view>

          <!-- 详细信息 -->
          <view class="detail-info">
            <view class="info-item">
              <text class="info-label">首付金额：</text>
              <text class="info-value">{{ result.downPayment }}万元</text>
            </view>
            <view class="info-item">
              <text class="info-label">贷款年限：</text>
              <text class="info-value">{{ formData.loanYear }}年</text>
            </view>
            <view class="info-item">
              <text class="info-label">贷款利率：</text>
              <text class="info-value">{{ formData.interestRate }}%</text>
            </view>
            <view class="info-item">
              <text class="info-label">还款方式：</text>
              <text class="info-value">{{ getPaymentMethodLabel() }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 表单数据
const formData = reactive({
  totalPrice: "",
  downPaymentRatio: 30,
  loanYear: 30,
  interestRate: "4.9",
  paymentMethod: "equal_payment", // equal_payment: 等额本息, equal_principal: 等额本金
});

// 计算结果
const result = ref(null);

// 首付比例选项
const downPaymentRatios = [20, 30, 40, 50, 60, 70];

// 贷款年限选项
const loanYears = [10, 15, 20, 25, 30];

// 还款方式选项
const paymentMethods = [
  { label: "等额本息", value: "equal_payment" },
  { label: "等额本金", value: "equal_principal" },
];

// 选择首付比例
const selectDownPaymentRatio = (ratio: number) => {
  formData.downPaymentRatio = ratio;
};

// 选择贷款年限
const selectLoanYear = (year: number) => {
  formData.loanYear = year;
};

// 选择还款方式
const selectPaymentMethod = (method: string) => {
  formData.paymentMethod = method;
};

// 获取还款方式标签
const getPaymentMethodLabel = () => {
  const method = paymentMethods.find(m => m.value === formData.paymentMethod);
  return method ? method.label : "";
};

// 计算房贷
const calculateLoan = () => {
  if (!formData.totalPrice || !formData.interestRate) {
    uni.showToast({
      title: "请填写完整信息",
      icon: "none",
    });
    return;
  }

  const totalPrice = parseFloat(formData.totalPrice);
  const downPaymentRatio = formData.downPaymentRatio / 100;
  const interestRate = parseFloat(formData.interestRate) / 100;
  const loanYear = formData.loanYear;

  // 计算基础数据
  const downPayment = totalPrice * downPaymentRatio;
  const loanAmount = totalPrice - downPayment;
  const monthlyRate = interestRate / 12;
  const totalMonths = loanYear * 12;

  let monthlyPayment = 0;
  let totalInterest = 0;

  if (formData.paymentMethod === "equal_payment") {
    // 等额本息
    monthlyPayment = (loanAmount * 10000 * monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) /
      (Math.pow(1 + monthlyRate, totalMonths) - 1);
    totalInterest = (monthlyPayment * totalMonths) / 10000 - loanAmount;
  } else {
    // 等额本金
    const monthlyPrincipal = (loanAmount * 10000) / totalMonths;
    const firstMonthInterest = loanAmount * 10000 * monthlyRate;
    monthlyPayment = monthlyPrincipal + firstMonthInterest;
    totalInterest = (loanAmount * 10000 * monthlyRate * (totalMonths + 1)) / 2 / 10000;
  }

  const totalPayment = loanAmount + totalInterest;

  result.value = {
    downPayment: downPayment.toFixed(2),
    loanAmount: loanAmount.toFixed(2),
    monthlyPayment: Math.round(monthlyPayment),
    totalInterest: totalInterest.toFixed(2),
    totalPayment: totalPayment.toFixed(2),
  };
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.calculator-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.page-content {
  padding-top: 88rpx;
  padding-bottom: 30rpx;
}

.form-group {
  .form-label {
    color: #333;
  }

  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 0 20rpx;

    .form-input {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
      color: #333;
    }

    .input-unit {
      font-size: 26rpx;
      color: #666;
      margin-left: 10rpx;
    }
  }
}

.ratio-options,
.year-options,
.payment-methods {
  gap: 16rpx;
  flex-wrap: wrap;
}

.ratio-item,
.year-item,
.method-item {
  flex: 1;
  min-width: 100rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
  }
}

.calculate-btn {
  height: 80rpx;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  box-shadow: 0 4rpx 15rpx rgba(67, 233, 123, 0.3);

  .btn-text {
    font-size: 30rpx;
    font-weight: 600;
    color: white;
  }

  &:active {
    transform: scale(0.98);
  }
}

.key-data {
  .data-item {
    text-align: center;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .data-label {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .data-value {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.detail-info {
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    .info-label {
      font-size: 26rpx;
      color: #666;
    }

    .info-value {
      font-size: 26rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.color-primary { color: #667eea; }
.color-red { color: #fa5741; }
.color-orange { color: #ff8f00; }
.color-blue { color: #1976d2; }
</style>
