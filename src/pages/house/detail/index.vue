<template>
  <view class="house-detail-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @tap="goBack">
        <text class="i-carbon-arrow-left"></text>
      </view>
      <view class="title">{{ houseData.title || "房源详情" }}</view>
      <view class="share-btn" @tap="shareHouse">
        <text class="i-carbon-share"></text>
      </view>
    </view>

    <!-- 媒体轮播区域 -->
    <view class="media-container">
      <swiper
        class="media-swiper"
        circular
        indicator-dots
        autoplay
        :interval="5000"
        @change="onSwiperChange"
      >
        <swiper-item v-for="(item, index) in mediaList" :key="index">
          <view class="media-item">
            <!-- 视频内容 -->
            <video
              v-if="item.type === 'video'"
              :src="item.url"
              class="media-video"
              controls
              object-fit="cover"
            ></video>
            <!-- 图片内容 -->
            <image
              v-else
              :src="item.url"
              mode="aspectFill"
              class="media-image"
              @tap="previewImage(index)"
            ></image>
          </view>
        </swiper-item>
      </swiper>
      <!-- 媒体计数器 -->
      <view class="media-counter">
        {{ currentMediaIndex + 1 }}/{{ mediaList.length }}
      </view>
    </view>

    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 核心信息展示 -->
      <view class="core-info-section">
        <!-- 住宅售卖核心信息 -->
        <view
          v-if="houseData.type === 'second' || houseData.type === 'new'"
          class="core-info-residential"
        >
          <!-- 价格信息 -->
          <view class="price-info">
            <view class="total-price">
              <text class="price-value">{{ houseData.price || "--" }}</text>
              <text class="price-unit">万</text>
            </view>
            <text class="unit-price">{{ houseData.unitPrice || "--" }}</text>
          </view>

          <!-- 房屋核心参数 -->
          <view class="house-params">
            <view class="param-item">
              <text class="param-value">{{ houseData.layout || "--" }}</text>
              <text class="param-label">户型</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{ houseData.area || "--" }}</text>
              <text class="param-label">面积</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{ houseData.direction || "--" }}</text>
              <text class="param-label">朝向</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{
                houseData.decoration || "--"
              }}</text>
              <text class="param-label">装修</text>
            </view>
          </view>

          <!-- 其他信息 -->
          <view class="other-info">
            <view class="info-row">
              <text class="info-label">楼层</text>
              <text class="info-value">{{ houseData.floor || "--" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">建筑年代</text>
              <text class="info-value">{{ houseData.buildYear || "--" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">产权年限</text>
              <text class="info-value">{{
                houseData.propertyRights || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">房屋类型</text>
              <text class="info-value">{{ houseData.houseType || "--" }}</text>
            </view>
          </view>
        </view>

        <!-- 租房核心信息 -->
        <view v-if="houseData.type === 'rent'" class="core-info-rental">
          <!-- 租金信息 -->
          <view class="price-info">
            <view class="total-price">
              <text class="price-value">{{ houseData.price || "--" }}</text>
              <text class="price-unit">元/月</text>
            </view>
            <view class="payment-method">{{
              houseData.paymentMethod || "--"
            }}</view>
          </view>

          <!-- 房屋核心参数 -->
          <view class="house-params">
            <view class="param-item">
              <text class="param-value">{{ houseData.layout || "--" }}</text>
              <text class="param-label">户型</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{ houseData.area || "--" }}</text>
              <text class="param-label">面积</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{ houseData.direction || "--" }}</text>
              <text class="param-label">朝向</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{
                houseData.decoration || "--"
              }}</text>
              <text class="param-label">装修</text>
            </view>
          </view>

          <!-- 其他信息 -->
          <view class="other-info">
            <view class="info-row">
              <text class="info-label">出租方式</text>
              <text class="info-value">{{ houseData.rentType || "--" }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">可入住时间</text>
              <text class="info-value">{{
                houseData.availableDate || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">最短租期</text>
              <text class="info-value">{{
                houseData.minRentPeriod || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">楼层</text>
              <text class="info-value">{{ houseData.floor || "--" }}</text>
            </view>
          </view>
        </view>

        <!-- 商铺/写字楼核心信息 -->
        <view
          v-if="houseData.type === 'commercial'"
          class="core-info-commercial"
        >
          <!-- 价格信息 -->
          <view class="price-info">
            <view class="total-price">
              <text class="price-value">{{ houseData.price || "--" }}</text>
              <text class="price-unit">{{
                houseData.priceUnit || "元/㎡/天"
              }}</text>
            </view>
            <text class="property-fee"
              >物业费：{{ houseData.propertyFee || "--" }}</text
            >
          </view>

          <!-- 房屋核心参数 -->
          <view class="house-params">
            <view class="param-item">
              <text class="param-value">{{ houseData.area || "--" }}</text>
              <text class="param-label">面积</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{ houseData.floor || "--" }}</text>
              <text class="param-label">楼层</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{
                houseData.decoration || "--"
              }}</text>
              <text class="param-label">装修</text>
            </view>
            <view class="param-item">
              <text class="param-value">{{
                houseData.canDivide ? "可分割" : "不可分割"
              }}</text>
              <text class="param-label">分割</text>
            </view>
          </view>

          <!-- 其他信息 -->
          <view class="other-info">
            <view class="info-row">
              <text class="info-label">类型</text>
              <text class="info-value">{{
                houseData.commercialType || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">可入驻时间</text>
              <text class="info-value">{{
                houseData.availableDate || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">最短租期</text>
              <text class="info-value">{{
                houseData.minRentPeriod || "--"
              }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">电梯</text>
              <text class="info-value">{{ houseData.elevator || "--" }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 标签展示 -->
      <view class="tags-section">
        <view class="section-title">房源亮点</view>
        <view class="tags-container">
          <text
            v-for="(tag, index) in houseData.tags"
            :key="index"
            class="tag-item"
            >{{ tag }}</text
          >
        </view>
      </view>

      <!-- 房源详情 -->
      <view class="detail-section">
        <view class="section-title">房源详情</view>

        <!-- 住宅/租房详情 -->
        <block
          v-if="
            houseData.type === 'second' ||
            houseData.type === 'new' ||
            houseData.type === 'rent'
          "
        >
          <!-- 小区信息 -->
          <view class="detail-block" v-if="houseData.communityInfo">
            <view class="detail-subtitle">小区信息</view>
            <view class="detail-content">
              <view class="community-info">
                <view class="info-row">
                  <text class="info-label">小区名称</text>
                  <text class="info-value">{{
                    houseData.communityInfo.name || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">建成年代</text>
                  <text class="info-value">{{
                    houseData.communityInfo.buildYear || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">物业费</text>
                  <text class="info-value">{{
                    houseData.communityInfo.propertyFee || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">物业公司</text>
                  <text class="info-value">{{
                    houseData.communityInfo.propertyCompany || "--"
                  }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 房屋特色 -->
          <view class="detail-block">
            <view class="detail-subtitle">房屋特色</view>
            <view class="detail-content">
              <text class="detail-text">{{
                houseData.features?.join("；") || "暂无特色描述"
              }}</text>
            </view>
          </view>

          <!-- 配套设施 -->
          <view class="detail-block">
            <view class="detail-subtitle">配套设施</view>
            <view class="detail-content">
              <view class="facilities-list">
                <view
                  v-for="(facility, index) in houseData.facilities"
                  :key="index"
                  class="facility-item"
                >
                  <text class="facility-icon">●</text>
                  <text class="facility-name">{{ facility }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 看房须知 -->
          <view class="detail-block">
            <view class="detail-subtitle">看房须知</view>
            <view class="detail-content">
              <text class="detail-text">{{
                houseData.viewingNotice || "随时可以看房，提前预约"
              }}</text>
            </view>
          </view>
        </block>

        <!-- 商铺/写字楼详情 -->
        <block v-if="houseData.type === 'commercial'">
          <!-- 项目介绍 -->
          <view class="detail-block" v-if="houseData.projectInfo">
            <view class="detail-subtitle">项目介绍</view>
            <view class="detail-content">
              <view class="project-info">
                <view class="info-row">
                  <text class="info-label">项目名称</text>
                  <text class="info-value">{{
                    houseData.projectInfo.name || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">开发商</text>
                  <text class="info-value">{{
                    houseData.projectInfo.developer || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">建成年代</text>
                  <text class="info-value">{{
                    houseData.projectInfo.buildYear || "--"
                  }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">建筑类型</text>
                  <text class="info-value">{{
                    houseData.projectInfo.buildingType || "--"
                  }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 楼宇设施 -->
          <view class="detail-block">
            <view class="detail-subtitle">楼宇设施</view>
            <view class="detail-content">
              <view class="facilities-list">
                <view
                  v-for="(facility, index) in houseData.buildingFacilities"
                  :key="index"
                  class="facility-item"
                >
                  <text class="facility-icon">●</text>
                  <text class="facility-name">{{ facility }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 物业服务 -->
          <view class="detail-block">
            <view class="detail-subtitle">物业服务</view>
            <view class="detail-content">
              <text class="detail-text">{{
                houseData.propertyService || "暂无物业服务描述"
              }}</text>
            </view>
          </view>

          <!-- 入驻企业 -->
          <view
            class="detail-block"
            v-if="houseData.tenants && houseData.tenants.length > 0"
          >
            <view class="detail-subtitle">入驻企业</view>
            <view class="detail-content">
              <view class="tenants-list">
                <text
                  v-for="(tenant, index) in houseData.tenants"
                  :key="index"
                  class="tenant-item"
                  >{{ tenant }}</text
                >
              </view>
            </view>
          </view>

          <!-- 经营范围 -->
          <view
            class="detail-block"
            v-if="houseData.businessScope && houseData.businessScope.length > 0"
          >
            <view class="detail-subtitle">经营范围</view>
            <view class="detail-content">
              <view class="business-scope-list">
                <text
                  v-for="(scope, index) in houseData.businessScope"
                  :key="index"
                  class="scope-item"
                  >{{ scope }}</text
                >
              </view>
            </view>
          </view>
        </block>
      </view>

      <!-- 位置及配套 -->
      <view class="location-section">
        <view class="section-title">位置及周边</view>

        <!-- 地址信息 -->
        <view class="address-info">
          <text class="address-text">{{
            houseData.address || "暂无地址信息"
          }}</text>
          <view class="navigate-btn" @tap="navigateToLocation">
            <text class="i-carbon-navigation"></text>
          </view>
        </view>

        <!-- 地图组件 -->
        <view class="map-container">
          <map
            class="location-map"
            :latitude="houseData.location.latitude"
            :longitude="houseData.location.longitude"
            :markers="[
              {
                id: 1,
                latitude: houseData.location.latitude,
                longitude: houseData.location.longitude,
                callout: {
                  content: houseData.title,
                  color: '#ffffff',
                  fontSize: 12,
                  borderRadius: 4,
                  bgColor: '#3B7FFF',
                  padding: 8,
                  display: 'ALWAYS',
                },
              },
            ]"
            scale="16"
          ></map>
        </view>

        <!-- 周边配套 -->
        <view
          class="surroundings-container"
          v-if="houseData.surroundings && houseData.surroundings.length > 0"
        >
          <scroll-view scroll-x class="surrounding-tabs">
            <view
              v-for="(category, index) in surroundingCategories"
              :key="index"
              class="tab-item"
              :class="{ active: currentSurroundingTab === index }"
              @tap="switchSurroundingTab(index)"
            >
              <text>{{ category.name }}</text>
            </view>
          </scroll-view>

          <view class="surrounding-list">
            <view
              v-for="(item, index) in filteredSurroundings"
              :key="index"
              class="surrounding-item"
            >
              <view class="surrounding-icon">
                <text
                  :class="surroundingCategories[currentSurroundingTab].icon"
                ></text>
              </view>
              <view class="surrounding-info">
                <text class="surrounding-name">{{ item.name }}</text>
                <text class="surrounding-distance">{{ item.distance }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 经纪人/顾问信息 -->
      <view class="agent-section">
        <!-- 这里将渲染经纪人信息 -->
      </view>

      <!-- 推荐房源 -->
      <view class="recommend-section">
        <!-- 这里将渲染推荐房源列表 -->
      </view>

      <!-- 底部占位，防止内容被底部操作栏遮挡 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 底部固定操作栏 -->
    <view class="action-bar">
      <!-- 这里将根据房源类型动态渲染不同操作按钮 -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";

// 定义页面参数类型
interface RouteParams {
  id: string;
  type: "rent" | "second" | "new" | "commercial";
}

// 房源详情数据
const houseData = reactive({
  id: "",
  title: "加载中...",
  type: "",
  price: "",
  unitPrice: "",
  layout: "",
  area: "",
  direction: "",
  decoration: "",
  floor: "",
  buildYear: "",
  propertyRights: "",
  houseType: "",
  paymentMethod: "",
  rentType: "",
  availableDate: "",
  minRentPeriod: "",
  propertyFee: "",
  priceUnit: "",
  commercialType: "",
  elevator: "",
  canDivide: false,
  tags: [],
  features: [],
  communityInfo: {},
  facilities: [],
  viewingNotice: "",
  projectInfo: {},
  buildingFacilities: [],
  propertyService: "",
  tenants: [],
  businessScope: [],
  address: "",
  location: { latitude: 0, longitude: 0 },
  surroundings: [],
});

// 媒体轮播数据
const mediaList = ref([]);
const currentMediaIndex = ref(0);

// 监听轮播切换事件
const onSwiperChange = (e) => {
  currentMediaIndex.value = e.detail.current;
};

// 预览图片
const previewImage = (index: number) => {
  const imageUrls = mediaList.value
    .filter((item) => item.type === "image")
    .map((item) => item.url);

  const imageIndex =
    mediaList.value.filter((item, idx) => item.type === "image" && idx <= index)
      .length - 1;

  uni.previewImage({
    urls: imageUrls,
    current: imageIndex,
  });
};

// 分享房源
const shareHouse = () => {
  uni.showToast({
    title: "分享功能开发中",
    icon: "none",
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载房源详情数据
const loadHouseDetail = async (id: string, type: string) => {
  // 模拟API请求
  setTimeout(() => {
    // 这里将根据不同类型的房源，加载不同的模拟数据
    // 实际开发中应该调用API获取真实数据

    // 模拟媒体数据
    mediaList.value = [
      {
        type: "image",
        url: "https://readdy.ai/api/search-image?query=modern%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520bright%2520living%2520room%2520with%2520contemporary%2520furniture%252C%2520hardwood%2520floors%252C%2520minimalist%2520design%252C%2520natural%2520lighting%252C%2520spacious%2520and%2520clean%252C%2520real%2520estate%2520photography%2520style%252C%2520professional%2520property%2520image&width=750&height=500",
      },
      {
        type: "image",
        url: "https://readdy.ai/api/search-image?query=apartment%2520kitchen%2520with%2520modern%2520design%252C%2520clean%2520countertops%252C%2520built-in%2520appliances%252C%2520wood%2520cabinets%252C%2520island%252C%2520real%2520estate%2520photography%2520style&width=750&height=500",
      },
      {
        type: "image",
        url: "https://readdy.ai/api/search-image?query=modern%2520bedroom%2520with%2520large%2520windows%252C%2520minimalist%2520design%252C%2520white%2520walls%252C%2520wooden%2520floor%252C%2520king%2520size%2520bed%252C%2520real%2520estate%2520photography%2520style&width=750&height=500",
      },
    ];

    // 更新房源基本信息
    Object.assign(houseData, {
      id,
      type,
      title: "南北通透三居室 精装修 满五唯一 业主诚心出售",
    });
  }, 500);
};

// 页面加载
onMounted(() => {
  const page = getCurrentPages().pop();
  // 使用any类型绕过类型检查
  const options = (page as any)?.options as RouteParams;

  if (options?.id) {
    loadHouseDetail(options.id, options.type || "second");
  } else {
    uni.showToast({
      title: "房源ID不存在",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 周边配套分类
const surroundingCategories = [
  { name: "交通", icon: "i-carbon-train", key: "transportation" },
  { name: "商业", icon: "i-carbon-shopping-bag", key: "commercial" },
  { name: "教育", icon: "i-carbon-education", key: "education" },
  { name: "医疗", icon: "i-carbon-hospital", key: "medical" },
  { name: "休闲", icon: "i-carbon-tree", key: "leisure" },
];

// 当前选中的周边配套标签
const currentSurroundingTab = ref(0);

// 切换周边配套标签
const switchSurroundingTab = (index: number) => {
  currentSurroundingTab.value = index;
};

// 过滤当前分类的周边配套
const filteredSurroundings = computed(() => {
  const category = surroundingCategories[currentSurroundingTab.value].key;
  return houseData.surroundings.filter((item) => item.category === category);
});

// 导航到房源位置
const navigateToLocation = () => {
  uni.openLocation({
    latitude: houseData.location.latitude,
    longitude: houseData.location.longitude,
    name: houseData.title,
    address: houseData.address,
    success: () => {
      console.log("导航成功");
    },
    fail: (err) => {
      console.error("导航失败", err);
      uni.showToast({
        title: "导航失败",
        icon: "none",
      });
    },
  });
};
</script>

<style lang="scss" scoped>
.house-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.back-btn,
.share-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 媒体轮播区域 */
.media-container {
  position: relative;
  width: 100%;
  height: 500rpx;
}

.media-swiper {
  width: 100%;
  height: 100%;
}

.media-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-video,
.media-image {
  width: 100%;
  height: 100%;
}

.media-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  width: 100%;
}

/* 各部分通用样式 */
.core-info-section,
.tags-section,
.detail-section,
.location-section,
.agent-section,
.recommend-section {
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
}

/* 底部空白占位 */
.bottom-placeholder {
  height: 100rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}
</style>
