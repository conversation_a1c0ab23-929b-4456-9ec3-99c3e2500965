<template>
  <view class="filter-panel">
    <!-- 筛选菜单栏 -->
    <view class="filter-menu sticky-header">
      <view
        v-for="(menu, index) in filterMenus"
        :key="index"
        class="filter-menu-item cursor-pointer"
        :class="{ active: currentFilterIndex === index }"
        @click="toggleFilterPanel(index)"
      >
        <text>{{ menu.name }}</text>
        <uni-icons
          :type="currentFilterIndex === index ? 'top' : 'bottom'"
          size="12"
          :color="currentFilterIndex === index ? '#3B7FFF' : '#666'"
        ></uni-icons>
      </view>
    </view>

    <!-- 详细筛选条件 -->
    <view class="filter-section" v-show="currentFilterIndex > -1">
      <view class="filter-content" v-show="currentFilterIndex === 0">
        <view class="filter-title">价格区间</view>
        <view class="price-options">
          <view
            v-for="(price, index) in priceOptions"
            :key="index"
            class="price-option cursor-pointer"
            :class="{ active: price.active }"
            @click="selectPrice(index)"
          >
            {{ price.label }}
          </view>
        </view>
      </view>

      <view class="filter-content" v-show="currentFilterIndex === 1">
        <view class="filter-title">户型选择</view>
        <view class="room-options">
          <view
            v-for="(room, index) in roomOptions"
            :key="index"
            class="room-option cursor-pointer"
            :class="{ active: room.active }"
            @click="selectRoom(index)"
          >
            {{ room.label }}
          </view>
        </view>
      </view>

      <view class="filter-content" v-show="currentFilterIndex === 2">
        <view class="filter-title">面积范围</view>
        <view class="area-options">
          <view
            v-for="(area, index) in areaOptions"
            :key="index"
            class="area-option cursor-pointer"
            :class="{ active: area.active }"
            @click="selectArea(index)"
          >
            {{ area.label }}
          </view>
        </view>
      </view>

      <view class="filter-content" v-show="currentFilterIndex === 3">
        <view class="filter-title">更多筛选</view>
        <view class="more-filters">
          <view
            class="more-filter-item cursor-pointer"
            @click="showMoreFilters"
          >
            <text>朝向/楼层/装修/年代等</text>
            <uni-icons type="right" size="14" color="#666"></uni-icons>
          </view>
        </view>
      </view>

      <view class="filter-actions">
        <view class="reset-btn cursor-pointer" @click="resetFilters">重置</view>
        <view class="confirm-btn cursor-pointer" @click="applyFilters"
          >确定</view
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";

// 接收初始选中的筛选类型索引
const props = defineProps({
  initialFilterIndex: {
    type: Number,
    default: -1,
  },
});

// 当前展开的筛选面板索引
const currentFilterIndex = ref(-1);

// 监听props变化，更新当前展开的筛选面板
watch(
  () => props.initialFilterIndex,
  (newVal) => {
    if (newVal !== undefined && newVal >= 0) {
      currentFilterIndex.value = newVal;
    }
  },
  { immediate: true }
);

// 筛选菜单
const filterMenus = reactive([
  { name: "价格", active: false },
  { name: "户型", active: false },
  { name: "面积", active: false },
  { name: "更多", active: false },
]);

// 价格区间选项
const priceOptions = reactive([
  { label: "300万以下", value: [0, 300], active: false },
  { label: "300-500万", value: [300, 500], active: false },
  { label: "500-800万", value: [500, 800], active: false },
  { label: "800-1000万", value: [800, 1000], active: false },
  { label: "1000万以上", value: [1000, null], active: false },
]);

// 户型选项
const roomOptions = reactive([
  { label: "一室", value: 1, active: false },
  { label: "二室", value: 2, active: false },
  { label: "三室", value: 3, active: false },
  { label: "四室", value: 4, active: false },
  { label: "五室及以上", value: 5, active: false },
]);

// 面积范围选项
const areaOptions = reactive([
  { label: "50㎡以下", value: [0, 50], active: false },
  { label: "50-70㎡", value: [50, 70], active: false },
  { label: "70-90㎡", value: [70, 90], active: false },
  { label: "90-110㎡", value: [90, 110], active: false },
  { label: "110-130㎡", value: [110, 130], active: false },
  { label: "130㎡以上", value: [130, null], active: false },
]);

// 定义事件
const emit = defineEmits(["update:visible", "filter-applied", "filter-closed"]);

// 切换筛选面板
const toggleFilterPanel = (index: number) => {
  if (currentFilterIndex.value === index) {
    currentFilterIndex.value = -1;
  } else {
    currentFilterIndex.value = index;
  }
};

// 选择价格区间
const selectPrice = (index: number) => {
  priceOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 选择户型
const selectRoom = (index: number) => {
  roomOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 选择面积
const selectArea = (index: number) => {
  areaOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 显示更多筛选条件
const showMoreFilters = () => {
  uni.showToast({
    title: "更多筛选功能开发中",
    icon: "none",
  });
};

// 重置筛选条件
const resetFilters = () => {
  priceOptions.forEach((option) => {
    option.active = false;
  });

  roomOptions.forEach((option) => {
    option.active = false;
  });

  areaOptions.forEach((option) => {
    option.active = false;
  });
};

// 应用筛选条件并关闭面板
const applyFilters = () => {
  // 收集选中的筛选条件
  const selectedFilters = {
    price: priceOptions.find((item) => item.active)?.value,
    room: roomOptions.find((item) => item.active)?.value,
    area: areaOptions.find((item) => item.active)?.value,
  };

  // 发送筛选条件给父组件
  emit("filter-applied", selectedFilters);

  // 关闭筛选面板
  currentFilterIndex.value = -1;
  emit("update:visible", false);
};

// 关闭筛选面板的方法，可由父组件调用
const closeFilter = () => {
  currentFilterIndex.value = -1;
  emit("filter-closed");
};

// 暴露方法给父组件
defineExpose({
  closeFilter,
});
</script>

<style lang="scss" scoped>
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 筛选菜单 */
.filter-menu {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.filter-menu-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666666;
  padding: 0 10rpx;
}

.filter-menu-item.active {
  color: #3b7fff;
  font-weight: 500;
}

.filter-menu-item text {
  margin-right: 4rpx;
}

/* 详细筛选条件 */
.filter-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
}

.filter-content {
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.price-options,
.room-options,
.area-options {
  display: flex;
  flex-wrap: wrap;
}

.price-option,
.room-option,
.area-option {
  width: 210rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f6f7;
  border-radius: 6rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 13px;
  color: #666666;
}

.price-option.active,
.room-option.active,
.area-option.active {
  background-color: rgba(59, 127, 255, 0.1);
  color: #3b7fff;
  font-weight: 500;
}

.more-filters {
  background-color: #f5f6f7;
  border-radius: 6rpx;
}

.more-filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  font-size: 13px;
  color: #666666;
}

.filter-actions {
  display: flex;
  margin-top: 40rpx;
  padding-bottom: 30rpx;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  font-size: 15px;
}

.reset-btn {
  background-color: #f5f6f7;
  color: #666666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #3b7fff;
  color: #ffffff;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
