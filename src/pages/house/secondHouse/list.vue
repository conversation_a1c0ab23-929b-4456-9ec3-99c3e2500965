<template>
  <view class="secondhand-container">
    <!-- 搜索区域 -->
    <view class="search-bar">
      <view class="location cursor-pointer">
        <text>北京</text>
        <uni-icons type="bottom" size="14" color="#333"></uni-icons>
      </view>
      <view class="search-input cursor-pointer">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <text class="placeholder">输入小区或商圈名开始找房</text>
      </view>
      <view class="map-btn cursor-pointer">
        <uni-icons type="location" size="22" color="#3B7FFF"></uni-icons>
      </view>
    </view>

    <!-- 筛选条件和房源列表 -->
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
    >
      <!-- 快速筛选标签 -->
      <scroll-view scroll-x class="quick-filter-scroll" show-scrollbar="false">
        <view class="quick-filter-tags">
          <view
            v-for="(tag, index) in quickFilterTags"
            :key="index"
            class="quick-tag cursor-pointer"
            :class="{ active: tag.active }"
            @click="toggleQuickTag(index)"
          >
            <text>{{ tag.name }}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 详细筛选条件 -->
      <view class="filter-section">
        <view class="filter-row">
          <view class="filter-title">价格区间</view>
          <view class="price-options">
            <view
              v-for="(price, index) in priceOptions"
              :key="index"
              class="price-option cursor-pointer"
              :class="{ active: price.active }"
              @click="selectPrice(index)"
            >
              {{ price.label }}
            </view>
          </view>
        </view>

        <view class="filter-row">
          <view class="filter-title">户型选择</view>
          <view class="room-options">
            <view
              v-for="(room, index) in roomOptions"
              :key="index"
              class="room-option cursor-pointer"
              :class="{ active: room.active }"
              @click="selectRoom(index)"
            >
              {{ room.label }}
            </view>
          </view>
        </view>

        <view class="filter-row">
          <view class="filter-title">面积范围</view>
          <view class="area-options">
            <view
              v-for="(area, index) in areaOptions"
              :key="index"
              class="area-option cursor-pointer"
              :class="{ active: area.active }"
              @click="selectArea(index)"
            >
              {{ area.label }}
            </view>
          </view>
        </view>

        <view class="filter-row">
          <view class="filter-title">更多筛选</view>
          <view class="more-filters">
            <view
              class="more-filter-item cursor-pointer"
              @click="showMoreFilters"
            >
              <text>朝向/楼层/装修/年代等</text>
              <uni-icons type="right" size="14" color="#666"></uni-icons>
            </view>
          </view>
        </view>

        <view class="filter-actions">
          <view class="reset-btn cursor-pointer" @click="resetFilters"
            >重置</view
          >
          <view class="confirm-btn cursor-pointer" @click="applyFilters"
            >确定</view
          >
        </view>
      </view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in secondHandHouses"
          :key="index"
          class="house-card cursor-pointer"
        >
          <image
            :src="house.image"
            mode="aspectFill"
            class="house-image"
          ></image>
          <view class="house-info">
            <text class="house-title">{{ house.title }}</text>
            <text class="house-location">{{ house.location }}</text>
            <view class="house-detail">
              <text>{{ house.roomType }}</text>
              <text class="separator">|</text>
              <text>{{ house.area }}</text>
              <text class="separator">|</text>
              <text>{{ house.direction }}</text>
              <text class="separator">|</text>
              <text>{{ house.floor }}</text>
            </view>
            <view class="house-tags">
              <text
                v-for="(tag, tagIndex) in house.tags"
                :key="tagIndex"
                class="tag"
                >{{ tag }}</text
              >
            </view>
            <view class="house-extra-info">
              <text class="build-year">{{ house.buildYear }}</text>
              <text class="property-right">{{ house.propertyRight }}</text>
            </view>
            <view class="house-price-info">
              <view class="price-total">
                <text class="house-price">{{ house.price }}</text>
                <text class="house-unit">万</text>
              </view>
              <text class="unit-price">{{ house.unitPrice }}</text>
            </view>
            <view class="special-tags">
              <text
                v-for="(specialTag, tagIndex) in house.specialTags"
                :key="tagIndex"
                class="special-tag"
                >{{ specialTag }}</text
              >
            </view>
          </view>
        </view>
        <view v-if="loading" class="loading">
          <uni-icons type="spinner-cycle" size="20" color="#3B7FFF"></uni-icons>
          <text>加载中...</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";

// 快速筛选标签
const quickFilterTags = reactive([
  { name: "近地铁", active: false },
  { name: "学区房", active: false },
  { name: "南北通透", active: false },
  { name: "电梯房", active: false },
  { name: "满五唯一", active: false },
  { name: "新上", active: false },
  { name: "降价", active: false },
  { name: "有钥匙", active: false },
]);

// 价格区间选项
const priceOptions = reactive([
  { label: "300万以下", value: [0, 300], active: false },
  { label: "300-500万", value: [300, 500], active: false },
  { label: "500-800万", value: [500, 800], active: false },
  { label: "800-1000万", value: [800, 1000], active: false },
  { label: "1000万以上", value: [1000, null], active: false },
]);

// 户型选项
const roomOptions = reactive([
  { label: "一室", value: 1, active: false },
  { label: "二室", value: 2, active: false },
  { label: "三室", value: 3, active: false },
  { label: "四室", value: 4, active: false },
  { label: "五室及以上", value: 5, active: false },
]);

// 面积范围选项
const areaOptions = reactive([
  { label: "50㎡以下", value: [0, 50], active: false },
  { label: "50-70㎡", value: [50, 70], active: false },
  { label: "70-90㎡", value: [70, 90], active: false },
  { label: "90-110㎡", value: [90, 110], active: false },
  { label: "110-130㎡", value: [110, 130], active: false },
  { label: "130㎡以上", value: [130, null], active: false },
]);

// 二手房列表数据
const secondHandHouses = reactive([
  {
    title: "南北通透三居室 精装修 满五唯一 业主诚心出售",
    location: "朝阳区 · 金茂府",
    roomType: "3室2厅",
    area: "128㎡",
    direction: "南北",
    floor: "中层/18层",
    tags: ["南北通透", "精装修", "近地铁"],
    buildYear: "建筑年代：2015年",
    propertyRight: "产权：70年",
    price: "890",
    unitPrice: "69,531元/㎡",
    specialTags: ["满五唯一", "随时看房"],
    image:
      "https://readdy.ai/api/search-image?query=modern%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520bright%2520living%2520room%2520with%2520contemporary%2520furniture%252C%2520hardwood%2520floors%252C%2520minimalist%2520design%252C%2520natural%2520lighting%252C%2520spacious%2520and%2520clean%252C%2520real%2520estate%2520photography%2520style%252C%2520professional%2520property%2520image&width=240&height=180&seq=8&orientation=landscape",
  },
  {
    title: "高层景观房 全明户型 精装修 拎包入住",
    location: "海淀区 · 万科如园",
    roomType: "4室2厅",
    area: "168㎡",
    direction: "东南",
    floor: "高层/26层",
    tags: ["高层景观", "全明户型", "学区房"],
    buildYear: "建筑年代：2018年",
    propertyRight: "产权：70年",
    price: "1280",
    unitPrice: "76,190元/㎡",
    specialTags: ["满二", "有钥匙"],
    image:
      "https://readdy.ai/api/search-image?query=luxury%2520condominium%2520with%2520city%2520view%252C%2520modern%2520high-rise%2520apartment%252C%2520large%2520balcony%252C%2520floor%2520to%2520ceiling%2520windows%252C%2520contemporary%2520design%252C%2520evening%2520lighting%252C%2520urban%2520living%252C%2520real%2520estate%2520photography&width=240&height=180&seq=9&orientation=landscape",
  },
  {
    title: "三室两厅 低楼层 采光好 交通便利 业主急售",
    location: "丰台区 · 保利熙悦",
    roomType: "3室2厅",
    area: "115㎡",
    direction: "南北",
    floor: "低层/18层",
    tags: ["低楼层", "采光好", "交通便利"],
    buildYear: "建筑年代：2012年",
    propertyRight: "产权：70年",
    price: "720",
    unitPrice: "62,608元/㎡",
    specialTags: ["满五", "业主急售"],
    image:
      "https://readdy.ai/api/search-image?query=bright%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520modern%2520living%2520room%252C%2520natural%2520light%252C%2520white%2520walls%252C%2520wooden%2520floors%252C%2520contemporary%2520furniture%252C%2520clean%2520and%2520spacious%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=10&orientation=landscape",
  },
  {
    title: "大四居 南北通透 环境优美 品质小区 近公园",
    location: "昌平区 · 华润置地公园九里",
    roomType: "4室2厅",
    area: "198㎡",
    direction: "南北",
    floor: "中层/24层",
    tags: ["大四居", "南北通透", "环境优美"],
    buildYear: "建筑年代：2016年",
    propertyRight: "产权：70年",
    price: "1580",
    unitPrice: "79,797元/㎡",
    specialTags: ["满二", "随时看房"],
    image:
      "https://readdy.ai/api/search-image?query=luxury%2520residential%2520complex%2520with%2520garden%2520view%252C%2520modern%2520architecture%252C%2520green%2520surroundings%252C%2520family-friendly%2520environment%252C%2520spacious%2520outdoor%2520areas%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=12&orientation=landscape",
  },
  {
    title: "豪华装修 一线江景房 品质小区 名校学区",
    location: "通州区 · 远洋天著",
    roomType: "3室2厅",
    area: "145㎡",
    direction: "东南",
    floor: "高层/32层",
    tags: ["豪华装修", "江景房", "品质小区"],
    buildYear: "建筑年代：2019年",
    propertyRight: "产权：70年",
    price: "1680",
    unitPrice: "115,862元/㎡",
    specialTags: ["满二", "学区房"],
    image:
      "https://readdy.ai/api/search-image?query=luxury%2520apartment%2520with%2520river%2520view%252C%2520high-end%2520interior%2520design%252C%2520premium%2520finishes%252C%2520large%2520windows%252C%2520elegant%2520furniture%252C%2520sophisticated%2520decor%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=13&orientation=landscape",
  },
]);

// 加载状态
const loading = ref(false);
const isRefreshing = ref(false);

// 切换快速筛选标签
const toggleQuickTag = (index: number) => {
  quickFilterTags[index].active = !quickFilterTags[index].active;
};

// 选择价格区间
const selectPrice = (index: number) => {
  priceOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 选择户型
const selectRoom = (index: number) => {
  roomOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 选择面积
const selectArea = (index: number) => {
  areaOptions.forEach((option, i) => {
    option.active = i === index;
  });
};

// 显示更多筛选条件
const showMoreFilters = () => {
  uni.showToast({
    title: "更多筛选功能开发中",
    icon: "none",
  });
};

// 重置筛选条件
const resetFilters = () => {
  quickFilterTags.forEach((tag) => {
    tag.active = false;
  });

  priceOptions.forEach((option) => {
    option.active = false;
  });

  roomOptions.forEach((option) => {
    option.active = false;
  });

  areaOptions.forEach((option) => {
    option.active = false;
  });
};

// 应用筛选条件
const applyFilters = () => {
  uni.showToast({
    title: "筛选条件已应用",
    icon: "success",
  });
};

// 加载更多
const loadMore = () => {
  if (loading.value) return;
  loading.value = true;

  setTimeout(() => {
    // 模拟加载更多数据
    const newHouses = [
      {
        title: "精装三居 南向 采光好 近地铁 满五唯一",
        location: "大兴区 · 首开华润城",
        roomType: "3室2厅",
        area: "118㎡",
        direction: "南向",
        floor: "中层/22层",
        tags: ["精装修", "南向", "近地铁"],
        buildYear: "建筑年代：2017年",
        propertyRight: "产权：70年",
        price: "680",
        unitPrice: "57,627元/㎡",
        specialTags: ["满五唯一", "随时看房"],
        image:
          "https://readdy.ai/api/search-image?query=modern%2520three%2520bedroom%2520apartment%2520interior%252C%2520bright%2520living%2520space%252C%2520contemporary%2520design%252C%2520large%2520windows%252C%2520natural%2520light%252C%2520clean%2520aesthetic%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=14&orientation=landscape",
      },
      {
        title: "全新毛坯 大三居 视野开阔 赠送面积多",
        location: "顺义区 · 万科翡翠公园",
        roomType: "3室2厅",
        area: "142㎡",
        direction: "东南",
        floor: "高层/28层",
        tags: ["毛坯房", "大三居", "视野开阔"],
        buildYear: "建筑年代：2020年",
        propertyRight: "产权：70年",
        price: "950",
        unitPrice: "66,901元/㎡",
        specialTags: ["满二", "有钥匙"],
        image:
          "https://readdy.ai/api/search-image?query=empty%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520unfurnished%2520space%252C%2520white%2520walls%252C%2520concrete%2520floors%252C%2520spacious%2520rooms%252C%2520bright%2520natural%2520lighting%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=15&orientation=landscape",
      },
    ];

    secondHandHouses.push(...newHouses);
    loading.value = false;
  }, 1000);
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;

  setTimeout(() => {
    // 模拟刷新数据
    isRefreshing.value = false;
    uni.showToast({
      title: "刷新成功",
      icon: "success",
    });
  }, 1000);
};
</script>

<style lang="scss" scoped>
.secondhand-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f6f7;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 101;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.back-btn {
  padding: 10rpx;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.search-icon {
  padding: 10rpx;
}

/* 搜索区域 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #f0f0f0;
}

.location {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.location text {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-right: 4rpx;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #f5f6f7;
  border-radius: 35rpx;
  padding: 0 20rpx;
}

.search-input .placeholder {
  margin-left: 10rpx;
  font-size: 13px;
  color: #999999;
}

.map-btn {
  margin-left: 20rpx;
  padding: 10rpx;
  flex-shrink: 0;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  overflow: auto;
}

/* 快速筛选标签 */
.quick-filter-scroll {
  width: 100%;
  white-space: nowrap;
  background-color: #ffffff;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.quick-filter-tags {
  display: inline-flex;
  padding: 0 30rpx;
}

.quick-tag {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  background-color: #f5f6f7;
  border-radius: 30rpx;
  font-size: 13px;
  color: #666666;
}

.quick-tag.active {
  background-color: rgba(59, 127, 255, 0.1);
  color: #3b7fff;
}

/* 详细筛选条件 */
.filter-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.filter-row {
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.price-options,
.room-options,
.area-options {
  display: flex;
  flex-wrap: wrap;
}

.price-option,
.room-option,
.area-option {
  width: 210rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f6f7;
  border-radius: 6rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 13px;
  color: #666666;
}

.price-option.active,
.room-option.active,
.area-option.active {
  background-color: rgba(59, 127, 255, 0.1);
  color: #3b7fff;
  font-weight: 500;
}

.more-filters {
  background-color: #f5f6f7;
  border-radius: 6rpx;
}

.more-filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  font-size: 13px;
  color: #666666;
}

.filter-actions {
  display: flex;
  margin-top: 40rpx;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  font-size: 15px;
}

.reset-btn {
  background-color: #f5f6f7;
  color: #666666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #3b7fff;
  color: #ffffff;
}

/* 房源列表 */
.house-list {
  padding: 0 30rpx;
}

.house-card {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.house-image {
  width: 240rpx;
  height: 180rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.house-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
}

.house-title {
  font-size: 16px;
  color: #333333;
  font-weight: 600;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.house-location {
  font-size: 13px;
  color: #666666;
  margin-top: 6rpx;
}

.house-detail {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  font-size: 13px;
  color: #666666;
}

.separator {
  margin: 0 8rpx;
  color: #dddddd;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.tag {
  font-size: 12px;
  color: #3b7fff;
  background-color: rgba(59, 127, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.house-extra-info {
  display: flex;
  margin-top: 10rpx;
  font-size: 12px;
  color: #999999;
}

.property-right {
  margin-left: 20rpx;
}

.house-price-info {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-top: 10rpx;
}

.price-total {
  display: flex;
  align-items: baseline;
}

.house-price {
  font-size: 18px;
  color: #ff5a5f;
  font-weight: 600;
}

.house-unit {
  font-size: 13px;
  color: #ff5a5f;
  margin-left: 4rpx;
}

.unit-price {
  font-size: 13px;
  color: #999999;
}

.special-tags {
  display: flex;
  margin-top: 10rpx;
}

.special-tag {
  font-size: 12px;
  color: #ff5a5f;
  background-color: rgba(255, 90, 95, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  color: #999999;
  font-size: 14px;
}

.loading text {
  margin-left: 10rpx;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
