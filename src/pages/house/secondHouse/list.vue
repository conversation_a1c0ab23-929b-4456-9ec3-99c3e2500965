<template>
  <view class="secondhand-container">
    <!-- 使用z-paging组件实现页面滚动 -->
    <z-paging
      ref="paging"
      v-model="secondHandHouses"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-scrollbar="false"
      @scrolltolower="loadMore"
      :refresher-threshold="80"
      :refresher-default-text="'下拉刷新'"
      :refresher-pulling-text="'释放刷新'"
      :refresher-refreshing-text="'刷新中...'"
    >
      <!-- 搜索区域 - 当筛选弹出层不可见时显示 -->
      <view class="search-bar" v-show="!isFilterVisible">
        <view class="location cursor-pointer">
          <text>北京</text>
          <uni-icons type="bottom" size="14" color="#333"></uni-icons>
        </view>
        <view class="search-input cursor-pointer">
          <uni-icons type="search" size="16" color="#999"></uni-icons>
          <text class="placeholder">输入小区或商圈名开始找房</text>
        </view>
      </view>

      <!-- 筛选菜单按钮栏 -->
      <view class="filter-buttons sticky-header">
        <view
          v-for="(menu, index) in filterMenus"
          :key="index"
          class="filter-button cursor-pointer"
          :class="{ active: activeFilterIndex === index }"
          @click="toggleFilter(index)"
        >
          <text>{{ menu.name }}</text>
          <uni-icons
            :type="activeFilterIndex === index ? 'top' : 'bottom'"
            size="12"
            :color="activeFilterIndex === index ? '#3B7FFF' : '#666'"
          ></uni-icons>
        </view>
      </view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in secondHandHouses"
          :key="index"
          class="house-card cursor-pointer"
          @click="navigateToDetail(house)"
        >
          <image
            :src="house.image"
            mode="aspectFill"
            class="house-image"
          ></image>
          <view class="house-info">
            <view class="house-info-top">
              <text class="house-title">{{ house.title }}</text>
              <view class="house-detail">
                <text>{{ house.roomType }}</text>
                <text class="separator">|</text>
                <text>{{ house.area }}</text>
                <text class="separator">|</text>
                <text>{{ house.direction }}</text>
                <text class="separator">|</text>
                <text>{{ house.location }}</text>
              </view>
              <view class="house-tags">
                <text
                  v-for="(tag, tagIndex) in house.tags"
                  :key="tagIndex"
                  class="tag"
                  >{{ tag }}</text
                >
              </view>
            </view>
            <view class="house-info-bottom">
              <view class="house-price-info">
                <view class="price-total">
                  <text class="house-price">{{ house.price }}</text>
                  <text class="house-unit">万</text>
                </view>
                <text class="unit-price">{{ house.unitPrice }}</text>
              </view>
              <view class="special-tags">
                <text
                  v-for="(specialTag, tagIndex) in house.specialTags"
                  :key="tagIndex"
                  class="special-tag"
                  >{{ specialTag }}</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <!-- 遮罩层 -->
    <view
      v-if="isFilterVisible"
      class="filter-mask"
      @click="closeFilter"
    ></view>

    <!-- 筛选弹出层组件 -->
    <filter-panel
      v-if="isFilterVisible"
      ref="filterPanel"
      :initial-filter-index="activeFilterIndex"
      v-model:visible="isFilterVisible"
      @filter-applied="applyFilters"
      @filter-closed="closeFilter"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import FilterPanel from "./components/FilterPanel.vue";

// 筛选菜单
const filterMenus = reactive([
  { name: "价格", active: false },
  { name: "户型", active: false },
  { name: "面积", active: false },
  { name: "更多", active: false },
]);

// 筛选相关状态
const isFilterVisible = ref(false);
const activeFilterIndex = ref(-1);
const filterPanel = ref(null);

// 切换筛选面板显示状态
const toggleFilter = (index: number) => {
  if (activeFilterIndex.value === index && isFilterVisible.value) {
    // 如果点击当前激活的筛选，则关闭筛选面板
    closeFilter();
  } else {
    // 否则显示对应的筛选面板
    activeFilterIndex.value = index;
    isFilterVisible.value = true;
  }
};

// 关闭筛选面板
const closeFilter = () => {
  isFilterVisible.value = false;
  activeFilterIndex.value = -1;
};

// 应用筛选条件
const applyFilters = (filters) => {
  console.log("应用筛选条件:", filters);
  // 重新加载列表
  paging.value.reload();
  uni.showToast({
    title: "筛选条件已应用",
    icon: "success",
  });
  closeFilter();
};

// 二手房列表数据
const secondHandHouses = ref([]);

// z-paging实例
const paging = ref(null);

// 加载状态
const loading = ref(false);
const isRefreshing = ref(false);

// 查询房源列表
const queryHouseList = (pageNo, pageSize) => {
  // 模拟异步请求
  loading.value = true;

  setTimeout(() => {
    // 模拟第一页数据
    if (pageNo === 1) {
      const firstPageData = [
        {
          title: "南北通透三居室 精装修 满五唯一 业主诚心出售",
          location: "金茂府",
          roomType: "3室2厅",
          area: "128㎡",
          direction: "南北",
          floor: "中层/18层",
          tags: ["南北通透", "精装修", "近地铁"],
          buildYear: "建筑年代：2015年",
          propertyRight: "产权：70年",
          price: "890",
          unitPrice: "69,531元/㎡",
          specialTags: ["满五唯一", "随时看房"],
          image:
            "https://readdy.ai/api/search-image?query=modern%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520bright%2520living%2520room%2520with%2520contemporary%2520furniture%252C%2520hardwood%2520floors%252C%2520minimalist%2520design%252C%2520natural%2520lighting%252C%2520spacious%2520and%2520clean%252C%2520real%2520estate%2520photography%2520style%252C%2520professional%2520property%2520image&width=240&height=180&seq=8&orientation=landscape",
        },
        {
          title: "高层景观房 全明户型 精装修 拎包入住",
          location: "万科如园",
          roomType: "4室2厅",
          area: "168㎡",
          direction: "东南",
          floor: "高层/26层",
          tags: ["高层景观", "全明户型", "学区房"],
          buildYear: "建筑年代：2018年",
          propertyRight: "产权：70年",
          price: "1280",
          unitPrice: "76,190元/㎡",
          specialTags: ["满二", "有钥匙"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520condominium%2520with%2520city%2520view%252C%2520modern%2520high-rise%2520apartment%252C%2520large%2520balcony%252C%2520floor%2520to%2520ceiling%2520windows%252C%2520contemporary%2520design%252C%2520evening%2520lighting%252C%2520urban%2520living%252C%2520real%2520estate%2520photography&width=240&height=180&seq=9&orientation=landscape",
        },
        {
          title: "三室两厅 低楼层 采光好 交通便利 业主急售",
          location: "保利熙悦",
          roomType: "3室2厅",
          area: "115㎡",
          direction: "南北",
          floor: "低层/18层",
          tags: ["低楼层", "采光好", "交通便利"],
          buildYear: "建筑年代：2012年",
          propertyRight: "产权：70年",
          price: "720",
          unitPrice: "62,608元/㎡",
          specialTags: ["满五", "业主急售"],
          image:
            "https://readdy.ai/api/search-image?query=bright%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520modern%2520living%2520room%252C%2520natural%2520light%252C%2520white%2520walls%252C%2520wooden%2520floors%252C%2520contemporary%2520furniture%252C%2520clean%2520and%2520spacious%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=10&orientation=landscape",
        },
        {
          title: "大四居 南北通透 环境优美 品质小区 近公园",
          location: "华润置地公园九里",
          roomType: "4室2厅",
          area: "198㎡",
          direction: "南北",
          floor: "中层/24层",
          tags: ["大四居", "南北通透", "环境优美"],
          buildYear: "建筑年代：2016年",
          propertyRight: "产权：70年",
          price: "1580",
          unitPrice: "79,797元/㎡",
          specialTags: ["满二", "随时看房"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520residential%2520complex%2520with%2520garden%2520view%252C%2520modern%2520architecture%252C%2520green%2520surroundings%252C%2520family-friendly%2520environment%252C%2520spacious%2520outdoor%2520areas%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=12&orientation=landscape",
        },
        {
          title: "豪华装修 一线江景房 品质小区 名校学区",
          location: "远洋天著",
          roomType: "3室2厅",
          area: "145㎡",
          direction: "东南",
          floor: "高层/32层",
          tags: ["豪华装修", "江景房", "品质小区"],
          buildYear: "建筑年代：2019年",
          propertyRight: "产权：70年",
          price: "1680",
          unitPrice: "115,862元/㎡",
          specialTags: ["满二", "学区房"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520apartment%2520with%2520river%2520view%252C%2520high-end%2520interior%2520design%252C%2520premium%2520finishes%252C%2520large%2520windows%252C%2520elegant%2520furniture%252C%2520sophisticated%2520decor%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=13&orientation=landscape",
        },
      ];
      // 告知z-paging加载完成
      paging.value.complete(firstPageData);
    } else {
      // 模拟加载更多数据
      const moreData = [
        {
          title: "精装三居 南向 采光好 近地铁 满五唯一",
          location: "大兴区 · 首开华润城",
          roomType: "3室2厅",
          area: "118㎡",
          direction: "南向",
          floor: "中层/22层",
          tags: ["精装修", "南向", "近地铁"],
          buildYear: "建筑年代：2017年",
          propertyRight: "产权：70年",
          price: "680",
          unitPrice: "57,627元/㎡",
          specialTags: ["满五唯一", "随时看房"],
          image:
            "https://readdy.ai/api/search-image?query=modern%2520three%2520bedroom%2520apartment%2520interior%252C%2520bright%2520living%2520space%252C%2520contemporary%2520design%252C%2520large%2520windows%252C%2520natural%2520light%252C%2520clean%2520aesthetic%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=14&orientation=landscape",
        },
        {
          title: "全新毛坯 大三居 视野开阔 赠送面积多",
          location: "顺义区 · 万科翡翠公园",
          roomType: "3室2厅",
          area: "142㎡",
          direction: "东南",
          floor: "高层/28层",
          tags: ["毛坯房", "大三居", "视野开阔"],
          buildYear: "建筑年代：2020年",
          propertyRight: "产权：70年",
          price: "950",
          unitPrice: "66,901元/㎡",
          specialTags: ["满二", "有钥匙"],
          image:
            "https://readdy.ai/api/search-image?query=empty%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520unfurnished%2520space%252C%2520white%2520walls%252C%2520concrete%2520floors%252C%2520spacious%2520rooms%252C%2520bright%2520natural%2520lighting%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=15&orientation=landscape",
        },
      ];
      // 告知z-paging加载完成
      paging.value.complete(moreData);
    }
    loading.value = false;
  }, 1000);
};

// 加载更多 - 这个方法在z-paging中已自动处理，这里不需要实现
const loadMore = () => {
  // z-paging会自动处理加载更多逻辑
};

// 导航到详情页
const navigateToDetail = (house) => {
  uni.navigateTo({
    url: `/pages/house/secondHouse/detail?id=${house.id || "1"}`,
  });
};
</script>

<style lang="scss" scoped>
.secondhand-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f6f7;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 101;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.back-btn {
  padding: 10rpx;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.search-icon {
  padding: 10rpx;
}

/* 搜索区域 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
}

.location {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.location text {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-right: 4rpx;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #ffffff;
  border-radius: 35rpx;
  padding: 0 20rpx;
  border: 1px solid #eee;
}

.search-input .placeholder {
  margin-left: 10rpx;
  font-size: 13px;
  color: #999999;
}

/* 筛选菜单按钮栏 */
.sticky-header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 99;
}

.filter-buttons {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.filter-button {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666666;
  padding: 10rpx 20rpx;
}

.filter-button.active {
  color: #3b7fff;
  font-weight: 500;
}

.filter-button text {
  margin-right: 4rpx;
}

/* 房源列表 */
.house-list {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}

.house-card {
  display: flex;
  border-radius: 12rpx;
  margin-bottom: 28rpx;
}

.house-image {
  width: 240rpx;
  height: 230rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  object-fit: cover;
}

.house-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.house-info-top {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.house-title {
  font-size: 16px;
  color: #333333;
  font-weight: 600;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  margin-bottom: 6rpx;
}

.house-detail {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
  font-size: 13px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.house-detail text {
  flex-shrink: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.separator {
  margin: 0 8rpx;
  color: #dddddd;
  flex-shrink: 0;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 6rpx;
  min-height: 38rpx;
}

.tag {
  font-size: 12px;
  color: #3b7fff;
  background-color: rgba(59, 127, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

.house-info-bottom {
  display: flex;
  flex-direction: column;
}

.house-price-info {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 6rpx;
}

.price-total {
  display: flex;
  align-items: baseline;
}

.house-price {
  font-size: 18px;
  color: #ff5a5f;
  font-weight: 600;
}

.house-unit {
  font-size: 13px;
  color: #ff5a5f;
  margin-left: 4rpx;
}

.unit-price {
  font-size: 13px;
  color: #999999;
}

.special-tags {
  display: flex;
  flex-wrap: wrap;
}

.special-tag {
  font-size: 12px;
  color: #ff5a5f;
  background-color: rgba(255, 90, 95, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 0;
}

.cursor-pointer {
  cursor: pointer;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
}
</style>
