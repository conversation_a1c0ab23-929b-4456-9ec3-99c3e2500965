<template>
  <view class="second-house-detail">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content flex items-center px-30rpx">
        <view
          class="back-btn flex items-center justify-center"
          @tap="navigateBack"
        >
          <text class="i-carbon-arrow-left text-32rpx"></text>
        </view>
        <view class="title flex-1 text-center text-32rpx font-bold"
          >房源详情</view
        >
        <view class="right-btns flex items-center">
          <view
            class="share-btn flex items-center justify-center mr-30rpx"
            @tap="showShareOptions"
          >
            <text class="i-carbon-share text-36rpx"></text>
          </view>
          <view
            class="favor-btn flex items-center justify-center"
            @tap="toggleFavorite"
          >
            <text
              :class="
                isFavorite
                  ? 'i-carbon-favorite-filled text-red'
                  : 'i-carbon-favorite'
              "
              class="text-36rpx"
            ></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 轮播图 -->
    <swiper
      class="house-swiper"
      :indicator-dots="true"
      :autoplay="true"
      :interval="4000"
      :duration="500"
      :circular="true"
    >
      <swiper-item v-for="(img, index) in houseDetail.images" :key="index">
        <view class="swiper-item">
          <image
            :src="img"
            mode="aspectFill"
            class="swiper-image"
            @tap="previewImage(index)"
          ></image>
          <view class="image-count">
            <text class="count-text"
              >{{ index + 1 }}/{{ houseDetail.images.length }}</text
            >
          </view>
          <view
            class="vr-btn"
            v-if="houseDetail.hasVR && index === 0"
            @tap.stop="openVR"
          >
            <text class="vr-text">VR看房</text>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 房源基本信息 -->
    <view
      class="info-section bg-white rounded-lg mx-20rpx mt-20rpx p-30rpx shadow-sm"
    >
      <view class="price-line flex items-center">
        <view>
          <text class="price text-40rpx font-bold text-red"
            >{{ houseDetail.price }}万</text
          >
          <text class="unit-price text-28rpx color-grey ml-20rpx"
            >{{ houseDetail.unitPrice }}元/㎡</text
          >
        </view>
        <view class="price-trend ml-20rpx" v-if="houseDetail.priceTrend">
          <text
            :class="[
              'trend-text',
              houseDetail.priceTrend > 0
                ? 'bg-red-50 text-red'
                : 'bg-green-50 text-green-600',
            ]"
          >
            {{ houseDetail.priceTrend > 0 ? "+" : ""
            }}{{ houseDetail.priceTrend }}%
          </text>
        </view>
      </view>

      <view class="title text-32rpx font-bold mt-20rpx">
        {{ houseDetail.title }}
      </view>

      <view class="tags flex flex-wrap mt-20rpx">
        <text
          v-for="(tag, index) in houseDetail.tags"
          :key="index"
          class="tag-item"
          >{{ tag }}</text
        >
      </view>

      <view class="house-attrs grid grid-cols-3 gap-y-20rpx mt-30rpx">
        <view class="attr-item">
          <text class="attr-label color-grey">房屋户型</text>
          <text class="attr-value">{{ houseDetail.layout }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">建筑面积</text>
          <text class="attr-value">{{ houseDetail.area }}㎡</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">单价</text>
          <text class="attr-value">{{ houseDetail.unitPrice }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">朝向</text>
          <text class="attr-value">{{ houseDetail.direction }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">装修</text>
          <text class="attr-value">{{ houseDetail.decoration }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">楼层</text>
          <text class="attr-value">{{ houseDetail.floor }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">房龄</text>
          <text class="attr-value">{{ houseDetail.age }}年</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">电梯</text>
          <text class="attr-value">{{
            houseDetail.hasElevator ? "有" : "无"
          }}</text>
        </view>
        <view class="attr-item">
          <text class="attr-label color-grey">类型</text>
          <text class="attr-value">{{ houseDetail.houseType }}</text>
        </view>
      </view>
    </view>

    <!-- 房源位置信息 -->
    <view
      class="location-section bg-white rounded-lg mx-20rpx mt-20rpx p-30rpx shadow-sm"
    >
      <view class="section-title flex items-center mb-20rpx">
        <view class="title-icon flex items-center justify-center mr-16rpx">
          <text class="i-carbon-location text-32rpx text-orange"></text>
        </view>
        <text class="text-32rpx font-bold">房源位置</text>
      </view>

      <view class="location-detail mb-20rpx">
        <text class="text-30rpx">{{ houseDetail.communityName }}</text>
        <text class="location-address block text-26rpx color-grey mt-10rpx">{{
          houseDetail.address
        }}</text>
      </view>

      <view class="map-container relative" @tap="openMap">
        <image
          src="https://picsum.photos/seed/map/750/300"
          mode="aspectFill"
          class="map-image rounded-lg"
        ></image>
        <view class="map-mask absolute flex items-center justify-center">
          <text class="map-tip">点击查看地图</text>
        </view>
      </view>

      <!-- 交通和周边 -->
      <view class="surroundings mt-30rpx">
        <view
          class="surrounding-item flex mb-16rpx"
          v-for="(item, index) in houseDetail.surroundings"
          :key="index"
        >
          <view
            :class="[
              'surrounding-icon flex items-center justify-center mr-20rpx',
              item.type === 'subway'
                ? 'bg-blue-500'
                : item.type === 'school'
                ? 'bg-green-500'
                : item.type === 'hospital'
                ? 'bg-red-500'
                : item.type === 'shop'
                ? 'bg-purple-500'
                : 'bg-orange-500',
            ]"
          >
            <text
              :class="[
                item.type === 'subway'
                  ? 'i-carbon-train'
                  : item.type === 'school'
                  ? 'i-carbon-education'
                  : item.type === 'hospital'
                  ? 'i-carbon-hospital'
                  : item.type === 'shop'
                  ? 'i-carbon-shopping-cart'
                  : 'i-carbon-restaurant',
                'text-white text-28rpx',
              ]"
            ></text>
          </view>
          <view class="surrounding-info">
            <view class="flex items-center justify-between">
              <text class="surrounding-name text-28rpx">{{ item.name }}</text>
              <text class="surrounding-distance text-26rpx color-grey">{{
                item.distance
              }}</text>
            </view>
            <text
              v-if="item.line"
              class="surrounding-line text-26rpx color-grey"
              >{{ item.line }}</text
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 房源特色 -->
    <view
      class="features-section bg-white rounded-lg mx-20rpx mt-20rpx p-30rpx shadow-sm"
    >
      <view class="section-title flex items-center mb-30rpx">
        <view class="title-icon flex items-center justify-center mr-16rpx">
          <text class="i-carbon-star text-32rpx text-orange"></text>
        </view>
        <text class="text-32rpx font-bold">房源特色</text>
      </view>

      <view
        class="feature-group mb-30rpx"
        v-for="(group, groupIndex) in houseDetail.featureGroups"
        :key="groupIndex"
      >
        <text class="feature-group-title text-30rpx font-bold mb-20rpx block">{{
          group.title
        }}</text>
        <text class="feature-content text-28rpx color-grey line-height-1-6">{{
          group.content
        }}</text>
      </view>
    </view>

    <!-- 房源配置 -->
    <view
      class="facilities-section bg-white rounded-lg mx-20rpx mt-20rpx p-30rpx shadow-sm"
    >
      <view class="section-title flex items-center mb-30rpx">
        <view class="title-icon flex items-center justify-center mr-16rpx">
          <text class="i-carbon-home text-32rpx text-orange"></text>
        </view>
        <text class="text-32rpx font-bold">房源配置</text>
      </view>

      <view class="facilities-grid grid grid-cols-4 gap-y-30rpx">
        <view
          v-for="(item, index) in houseDetail.facilities"
          :key="index"
          class="facility-item flex flex-col items-center"
          :class="{ 'facility-inactive': !item.available }"
        >
          <text
            :class="[
              item.icon,
              'text-36rpx',
              item.available ? 'text-primary' : 'text-gray-300',
            ]"
          ></text>
          <text class="facility-name text-24rpx mt-8rpx">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 相似房源推荐 -->
    <view
      class="similar-section bg-white rounded-lg mx-20rpx mt-20rpx mb-120rpx p-30rpx shadow-sm"
    >
      <view class="section-title flex items-center justify-between mb-30rpx">
        <view class="flex items-center">
          <view class="title-icon flex items-center justify-center mr-16rpx">
            <text class="i-carbon-recommendation text-32rpx text-orange"></text>
          </view>
          <text class="text-32rpx font-bold">相似房源</text>
        </view>
        <view class="view-more flex items-center" @tap="viewMoreSimilar">
          <text class="text-26rpx color-grey">查看更多</text>
          <text
            class="i-carbon-arrow-right text-24rpx color-grey ml-6rpx"
          ></text>
        </view>
      </view>

      <scroll-view scroll-x class="similar-scroll">
        <view class="similar-list flex">
          <view
            v-for="(item, index) in similarHouses"
            :key="index"
            class="similar-item mr-20rpx"
            @tap="navigateToHouse(item.id)"
          >
            <image
              :src="item.image"
              mode="aspectFill"
              class="similar-image rounded-lg"
            ></image>
            <view class="similar-info p-16rpx">
              <text class="similar-title text-28rpx font-bold line-clamp-1">{{
                item.title
              }}</text>
              <view class="similar-attrs text-24rpx color-grey mt-8rpx">
                <text>{{ item.layout }}</text>
                <text class="mx-10rpx">|</text>
                <text>{{ item.area }}㎡</text>
              </view>
              <view class="similar-price mt-8rpx">
                <text class="text-28rpx font-bold text-red"
                  >{{ item.price }}万</text
                >
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view
      class="bottom-bar flex items-center fixed left-0 right-0 bottom-0 bg-white px-30rpx"
    >
      <view class="consultant-area flex items-center flex-1">
        <image
          :src="houseDetail.agent.avatar"
          class="consultant-avatar"
        ></image>
        <view class="consultant-info ml-16rpx">
          <text class="consultant-name text-30rpx font-bold">{{
            houseDetail.agent.name
          }}</text>
          <text class="consultant-company text-24rpx color-grey">{{
            houseDetail.agent.company
          }}</text>
        </view>
      </view>
      <view class="action-btns flex">
        <button class="chat-btn" @tap="contactAgent('message')">
          在线咨询
        </button>
        <button class="call-btn" @tap="contactAgent('phone')">电话咨询</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref } from "vue";

// 状态栏高度
const statusBarHeight = ref(0);
// 是否收藏
const isFavorite = ref(false);

// 获取房源详情
const houseId = ref("");
const houseDetail = ref({
  id: "1001",
  title: "海淀区 西二旗 金域华府 南北通透大三居 精装修 满五唯一",
  images: [
    "https://picsum.photos/seed/house1/800/600",
    "https://picsum.photos/seed/house2/800/600",
    "https://picsum.photos/seed/house3/800/600",
    "https://picsum.photos/seed/house4/800/600",
    "https://picsum.photos/seed/house5/800/600",
  ],
  hasVR: true,
  price: 580,
  unitPrice: 52800,
  priceTrend: -2.5, // 价格走势，正数表示涨，负数表示跌
  tags: ["满五唯一", "学区房", "地铁房", "精装修", "南北通透"],
  layout: "3室2厅2卫",
  area: 110,
  direction: "南北",
  decoration: "精装修",
  floor: "6/18层",
  age: 5,
  hasElevator: true,
  houseType: "普通住宅",
  communityName: "金域华府",
  address: "北京市海淀区西二旗大街1号",
  surroundings: [
    {
      type: "subway",
      name: "西二旗地铁站",
      distance: "350米",
      line: "13号线/昌平线",
    },
    { type: "school", name: "金域小学", distance: "500米" },
    { type: "shop", name: "永辉超市", distance: "800米" },
    { type: "hospital", name: "海淀区医院", distance: "1.2公里" },
    { type: "restaurant", name: "盒马鲜生", distance: "1.5公里" },
  ],
  featureGroups: [
    {
      title: "房源介绍",
      content:
        "此房满五年唯一住房，南北通透，精装修，户型方正，采光充足，三居室带独立书房，业主诚心出售，看房方便。距离地铁站仅需5分钟步行，生活便利，周边配套齐全。",
    },
    {
      title: "小区介绍",
      content:
        "金域华府是海淀区知名高端小区，绿化率高达40%，物业管理完善，安保24小时值守，小区环境优美，居住舒适度高。",
    },
    {
      title: "交易信息",
      content:
        "房屋满五年唯一住房，首付比例低，税费优惠。业主换房急售，价格可谈。",
    },
  ],
  facilities: [
    { name: "电视", icon: "i-carbon-screen", available: true },
    { name: "冰箱", icon: "i-carbon-cold-storage", available: true },
    { name: "洗衣机", icon: "i-carbon-wash-machine", available: true },
    { name: "空调", icon: "i-carbon-temperature", available: true },
    { name: "热水器", icon: "i-carbon-temperature-hot", available: true },
    { name: "宽带", icon: "i-carbon-network", available: true },
    { name: "衣柜", icon: "i-carbon-folder", available: true },
    { name: "天然气", icon: "i-carbon-fire", available: true },
    { name: "暖气", icon: "i-carbon-steam", available: true },
    { name: "微波炉", icon: "i-carbon-timer", available: false },
    { name: "油烟机", icon: "i-carbon-windy", available: true },
    { name: "沙发", icon: "i-carbon-sofa", available: true },
  ],
  agent: {
    id: "agent001",
    name: "王经理",
    avatar: "https://picsum.photos/seed/agent1/200/200",
    phone: "13800138000",
    company: "链家地产",
    rating: 4.8,
  },
});

// 相似房源数据
const similarHouses = ref([
  {
    id: "similar1",
    title: "金域华府 2室1厅 南北通透",
    image: "https://picsum.photos/seed/similar1/300/200",
    layout: "2室1厅",
    area: 89,
    price: 450,
  },
  {
    id: "similar2",
    title: "西山华府 3室2厅 精装修",
    image: "https://picsum.photos/seed/similar2/300/200",
    layout: "3室2厅",
    area: 120,
    price: 620,
  },
  {
    id: "similar3",
    title: "紫金长安 4室2厅 花园洋房",
    image: "https://picsum.photos/seed/similar3/300/200",
    layout: "4室2厅",
    area: 143,
    price: 850,
  },
  {
    id: "similar4",
    title: "万科城市花园 3室1厅",
    image: "https://picsum.photos/seed/similar4/300/200",
    layout: "3室1厅",
    area: 98,
    price: 520,
  },
]);

// 页面加载
onLoad((options) => {
  if (options && options.id) {
    houseId.value = options.id;
    // 这里应该调用API获取实际的房源详情
    // fetchHouseDetail(houseId.value);
  }

  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;

  // 检查是否已收藏
  checkFavorite();
});

// 检查是否收藏
const checkFavorite = () => {
  // 从本地存储或API检查收藏状态
  const favoriteHouses = uni.getStorageSync("favoriteHouses") || [];
  isFavorite.value = favoriteHouses.includes(houseId.value);
};

// 切换收藏状态
const toggleFavorite = () => {
  const favoriteHouses = uni.getStorageSync("favoriteHouses") || [];

  if (isFavorite.value) {
    // 取消收藏
    const index = favoriteHouses.indexOf(houseId.value);
    if (index > -1) {
      favoriteHouses.splice(index, 1);
    }
  } else {
    // 添加收藏
    favoriteHouses.push(houseId.value);
  }

  uni.setStorageSync("favoriteHouses", favoriteHouses);
  isFavorite.value = !isFavorite.value;

  uni.showToast({
    title: isFavorite.value ? "已收藏" : "已取消收藏",
    icon: "none",
  });
};

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: houseDetail.value.images,
  });
};

// 打开VR看房
const openVR = () => {
  uni.showToast({
    title: "暂未开放VR功能",
    icon: "none",
  });
};

// 打开地图
const openMap = () => {
  uni.showToast({
    title: "查看地图位置",
    icon: "none",
  });
  // 实际应该打开地图页面
};

// 查看更多相似房源
const viewMoreSimilar = () => {
  uni.navigateTo({
    url: "/pages/house/secondHouse/list?similar_to=" + houseId.value,
  });
};

// 导航到相似房源详情
const navigateToHouse = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/secondHouse/detail?id=${id}`,
  });
};

// 联系经纪人
const contactAgent = (type: "phone" | "message") => {
  if (type === "phone") {
    uni.makePhoneCall({
      phoneNumber: houseDetail.value.agent.phone,
    });
  } else {
    uni.navigateTo({
      url: `/pages/message/chat?targetId=${houseDetail.value.agent.id}&targetName=${houseDetail.value.agent.name}&targetType=agent`,
    });
  }
};

// 显示分享选项
const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ["shareAppMessage", "shareTimeline"],
  });
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.second-house-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}

.custom-navbar {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;

  .navbar-content {
    height: 90rpx;
  }

  .back-btn,
  .share-btn,
  .favor-btn {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .text-red {
    color: #ff4d4f;
  }
}

.house-swiper {
  width: 100%;
  height: 560rpx;
}

.swiper-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;

  .count-text {
    color: white;
    font-size: 24rpx;
  }
}

.vr-btn {
  position: absolute;
  left: 20rpx;
  bottom: 20rpx;
  background-color: #ff6d00;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;

  .vr-text {
    color: white;
    font-size: 24rpx;
    font-weight: bold;
  }
}

.info-section {
  .price-line {
    .price {
      color: #fa5741;
    }
  }

  .trend-text {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 6rpx;
  }

  .tag-item {
    background-color: #f6f6f6;
    color: #666;
    font-size: 22rpx;
    padding: 6rpx 16rpx;
    border-radius: 6rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
  }

  .house-attrs {
    .attr-item {
      display: flex;
      flex-direction: column;

      .attr-label {
        font-size: 24rpx;
        margin-bottom: 8rpx;
      }

      .attr-value {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

.location-section {
  .title-icon,
  .facility-icon,
  .surrounding-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #fff8f0;
  }

  .text-orange {
    color: #ff6d00;
  }

  .map-container {
    width: 100%;
    height: 300rpx;
    overflow: hidden;

    .map-image {
      width: 100%;
      height: 100%;
    }

    .map-mask {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      .map-tip {
        color: white;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 10rpx 30rpx;
        border-radius: 30rpx;
        font-size: 26rpx;
      }
    }
  }

  .surrounding-icon {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
  }
}

.features-section,
.facilities-section,
.similar-section {
  .title-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #fff8f0;
  }

  .text-orange {
    color: #ff6d00;
  }

  .line-height-1-6 {
    line-height: 1.6;
  }
}

.facilities-section {
  .facility-item {
    .text-primary {
      color: #ff6d00;
    }

    &.facility-inactive {
      opacity: 0.5;
    }
  }
}

.similar-scroll {
  white-space: nowrap;

  .similar-list {
    display: inline-flex;
    padding: 10rpx 0;
  }

  .similar-item {
    flex-shrink: 0;
    width: 240rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .similar-image {
      width: 240rpx;
      height: 160rpx;
    }

    .line-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .text-red {
      color: #fa5741;
    }
  }
}

.bottom-bar {
  height: 110rpx;
  border-top: 1px solid #eee;
  padding-bottom: env(safe-area-inset-bottom);

  .consultant-avatar {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
  }

  .action-btns {
    button {
      height: 72rpx;
      font-size: 28rpx;
      border-radius: 36rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 36rpx;
      margin: 0;
    }

    .chat-btn {
      background-color: #f8f8f8;
      color: #333;
      margin-right: 20rpx;
    }

    .call-btn {
      background-color: #ff6d00;
      color: white;
    }
  }
}

.color-grey {
  color: #999;
}
</style>
