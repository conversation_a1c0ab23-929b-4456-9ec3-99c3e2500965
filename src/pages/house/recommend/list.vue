<template>
  <view class="recommend-page">
    <!-- 导航栏 -->
    <uni-nav-bar
      :fixed="true"
      :border="false"
      background-color="transparent"
      color="#333"
      title="推荐房源"
      left-icon="left"
      @clickLeft="goBack"
    />

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 推荐标签 -->
      <view class="recommend-tags bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
        <view class="px-30rpx py-30rpx">
          <view class="section-title mb-20rpx">
            <text class="text-30rpx font-bold">为您推荐</text>
          </view>
          <scroll-view scroll-x class="tags-scroll">
            <view class="tags-container flex">
              <view
                v-for="(tag, index) in recommendTags"
                :key="index"
                class="tag-item"
                :class="{ 'active': activeTag === index }"
                @tap="switchTag(index)"
              >
                <text class="tag-icon" :class="tag.icon"></text>
                <text class="tag-text">{{ tag.name }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 房源列表 -->
      <view class="house-list px-20rpx">
        <view class="grid grid-cols-1 gap-20rpx">
          <HouseCard
            v-for="(item, index) in houseList"
            :key="item.id"
            :house="item"
            :type="item.type"
            @favorite-change="handleFavoriteChange"
            @share="handleShare"
          />
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more py-40rpx text-center">
          <uni-load-more :status="loadStatus" />
        </view>
        
        <!-- 空状态 -->
        <view
          v-if="houseList.length === 0 && !isLoading"
          class="empty-state flex flex-col items-center justify-center py-120rpx"
        >
          <view class="empty-icon mb-30rpx">
            <text class="i-solar-star-bold text-120rpx color-grey-light"></text>
          </view>
          <text class="empty-title text-32rpx color-grey mb-16rpx">暂无推荐</text>
          <text class="empty-desc text-26rpx color-grey-light">
            系统正在为您匹配合适的房源
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import HouseCard from "@/components/house/HouseCard.vue";

// 推荐标签
const recommendTags = [
  {
    name: "智能推荐",
    icon: "i-solar-magic-stick-3-bold",
    type: "ai",
  },
  {
    name: "价格优选",
    icon: "i-solar-dollar-minimalistic-bold",
    type: "price",
  },
  {
    name: "地段优质",
    icon: "i-solar-map-point-bold",
    type: "location",
  },
  {
    name: "户型优选",
    icon: "i-solar-home-2-bold",
    type: "layout",
  },
  {
    name: "新上房源",
    icon: "i-solar-star-bold",
    type: "new",
  },
];

// 当前激活的标签
const activeTag = ref(0);

// 房源列表
const houseList = ref([]);

// 加载状态
const isLoading = ref(false);
const hasMore = ref(true);
const loadStatus = ref("more");

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
});

// 页面加载
onLoad((options) => {
  loadRecommendHouses();
});

// 触底加载更多
onReachBottom(() => {
  if (hasMore.value && !isLoading.value) {
    loadMore();
  }
});

// 切换推荐标签
const switchTag = (index: number) => {
  activeTag.value = index;
  pagination.page = 1;
  houseList.value = [];
  loadRecommendHouses();
};

// 加载推荐房源
const loadRecommendHouses = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  loadStatus.value = "loading";
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockData = generateMockHouses();
    
    if (pagination.page === 1) {
      houseList.value = mockData;
    } else {
      houseList.value.push(...mockData);
    }
    
    hasMore.value = mockData.length === pagination.pageSize;
    loadStatus.value = hasMore.value ? "more" : "noMore";
    
  } catch (error) {
    console.error("加载推荐房源失败:", error);
    uni.showToast({
      title: "加载失败",
      icon: "error",
    });
    loadStatus.value = "more";
  } finally {
    isLoading.value = false;
  }
};

// 加载更多
const loadMore = () => {
  pagination.page++;
  loadRecommendHouses();
};

// 生成模拟数据
const generateMockHouses = () => {
  const types = ["second", "rent", "new"];
  const currentTag = recommendTags[activeTag.value];
  
  return Array.from({ length: pagination.pageSize }, (_, index) => {
    const type = types[Math.floor(Math.random() * types.length)];
    const id = Date.now() + index;
    
    return {
      id,
      type,
      title: `${currentTag.name} - 精选房源 ${id}`,
      image: `https://picsum.photos/400/300?random=${id}`,
      price: type === "rent" ? Math.floor(Math.random() * 5000) + 2000 : Math.floor(Math.random() * 500) + 200,
      area: Math.floor(Math.random() * 100) + 50,
      layout: `${Math.floor(Math.random() * 3) + 1}室${Math.floor(Math.random() * 2) + 1}厅`,
      location: "朝阳区",
      tags: ["推荐", currentTag.name, "优质"],
      isNew: Math.random() > 0.7,
      isVip: Math.random() > 0.8,
      isFavorite: false,
      viewCount: Math.floor(Math.random() * 1000) + 100,
    };
  });
};

// 处理收藏变化
const handleFavoriteChange = (house: any) => {
  house.isFavorite = !house.isFavorite;
  uni.showToast({
    title: house.isFavorite ? "已收藏" : "已取消收藏",
    icon: "success",
    duration: 1500,
  });
};

// 处理分享
const handleShare = (house: any) => {
  uni.showActionSheet({
    itemList: ["复制链接", "分享给朋友"],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.setClipboardData({
          data: `${house.title} - ${house.price}${house.type === 'rent' ? '元/月' : '万'}`,
          success: () => {
            uni.showToast({
              title: "链接已复制",
              icon: "success",
            });
          },
        });
      }
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.recommend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.page-content {
  padding-top: 88rpx;
  padding-bottom: 30rpx;
}

.recommend-tags {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .section-title text {
    color: white;
  }
}

.tags-scroll {
  white-space: nowrap;
}

.tags-container {
  gap: 20rpx;
}

.tag-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  
  &.active {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.05);
    
    .tag-icon {
      color: #667eea !important;
    }
    
    .tag-text {
      color: #333 !important;
      font-weight: 600;
    }
  }
  
  .tag-icon {
    font-size: 32rpx;
    color: white;
    margin-bottom: 8rpx;
    transition: color 0.3s ease;
  }
  
  .tag-text {
    font-size: 24rpx;
    color: white;
    transition: all 0.3s ease;
  }
}

.house-list {
  padding-bottom: 30rpx;
}

.empty-state {
  min-height: 400rpx;
}

.color-grey-light {
  color: #ccc;
}
</style>
