<template>
  <view class="house-card bg-white mb-20rpx rounded-lg overflow-hidden" @tap="navigateToDetail">
    <!-- 左侧图片区域 -->
    <view class="flex">
      <view class="image-container relative">
        <!-- 特殊标签：看房有礼、新房等 -->
        <view v-if="house.specialTag" class="special-tag">{{ house.specialTag }}</view>
        <!-- VR看房标签 -->
        <view v-if="house.hasVR" class="vr-tag flex items-center">
          <text class="i-carbon-view-360 mr-4rpx"></text>
          <text>VR看房</text>
        </view>
        <image :src="house.image" mode="aspectFill" class="house-image" :lazy-load="true"></image>
      </view>
      
      <!-- 右侧信息区域 -->
      <view class="info-container flex-1 py-16rpx pr-20rpx">
        <!-- 标题行 -->
        <view class="title-row">
          <!-- 人气好房标签 -->
          <view v-if="house.isHot" class="hot-tag">人气好房</view>
          <text class="house-title line-clamp-2">{{ house.title }}</text>
        </view>
        
        <!-- 户型信息行 -->
        <view class="house-info mt-10rpx">
          <text>{{ house.layout }}</text>
          <text class="mx-10rpx">{{ house.area }}㎡</text>
          <text>{{ house.direction }}</text>
          <text v-if="house.floor" class="mx-10rpx">{{ house.floor }}</text>
        </view>
        
        <!-- 位置信息行 -->
        <view class="location-info text-26rpx color-grey mt-6rpx line-clamp-1">
          {{ house.location || house.community }}
        </view>
        
        <!-- 标签行 -->
        <view class="tags-row flex flex-wrap mt-10rpx">
          <text v-for="(tag, index) in house.tags" :key="index" class="tag-item">{{ tag }}</text>
        </view>
        
        <!-- 价格行 -->
        <view class="price-row flex justify-between items-center mt-10rpx">
          <view class="price">
            <text class="price-value">{{ formatPrice(house.price) }}</text>
            <text class="unit-price">{{ formatUnitPrice(house.unitPrice) }}</text>
          </view>
          <view v-if="house.publishTime" class="publish-time text-24rpx color-grey">
            {{ house.publishTime }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  house: {
    type: Object,
    required: true
  },
  // 房源类型：second(二手房)、new(新房)、rent(租房)、commercial(商铺办公)
  type: {
    type: String,
    default: "second"
  }
});

// 格式化价格展示
const formatPrice = (price) => {
  if (!price) return '';
  
  if (typeof price === 'string') {
    return price;
  }
  
  switch (props.type) {
    case 'second':
      return `${price}万`;
    case 'new':
      return `${price}元/㎡起`;
    case 'rent':
      return `${price}元/月`;
    case 'commercial':
      return props.house.priceType === 'sale' ? `${price}万` : `${price}元/月`;
    default:
      return `${price}`;
  }
};

// 格式化单价
const formatUnitPrice = (unitPrice) => {
  if (!unitPrice) return '';
  
  if (typeof unitPrice === 'number') {
    return `${unitPrice.toLocaleString()}元/㎡`;
  }
  
  return unitPrice;
};

// 跳转到详情页
const navigateToDetail = () => {
  uni.navigateTo({
    url: `/pages/house/${props.type}/detail?id=${props.house.id}`
  });
};
</script>

<style lang="scss" scoped>
.house-card {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.image-container {
  width: 240rpx;
  height: 180rpx;
  margin-right: 20rpx;
}

.house-image {
  width: 240rpx;
  height: 180rpx;
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}

.special-tag {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #FF5A5F;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-top-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  z-index: 1;
}

.vr-tag {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  z-index: 1;
}

.hot-tag {
  display: inline-block;
  padding: 2rpx 12rpx;
  background-color: #FF5A5F;
  color: white;
  font-size: 22rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  vertical-align: middle;
}

.house-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}

.house-info {
  font-size: 28rpx;
  color: #333;
}

.line-clamp-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.tag-item {
  padding: 2rpx 10rpx;
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10rpx;
  font-size: 22rpx;
  border-radius: 4rpx;
  margin-bottom: 8rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF5A5F;
}

.unit-price {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.color-grey {
  color: #999;
}
</style>