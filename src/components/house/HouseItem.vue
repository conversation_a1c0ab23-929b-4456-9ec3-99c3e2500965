<template>
  <view
    class="house-item bg-white flex mb-20rpx rounded-lg relative shadow-sm transform transition-transform duration-200"
    :class="{ 'active-scale': isActive }"
    @touchstart="isActive = true"
    @touchend="isActive = false"
    @touchcancel="isActive = false"
    @tap="navigateToDetail"
  >
    <!-- 标记标签 -->
    <view
      v-if="house.newTag"
      class="new-tag absolute top-0 left-0 z-10 bg-red text-white text-20rpx py-4rpx px-10rpx"
    >
      新上
    </view>

    <view class="house-image-container mr-20rpx relative">
      <image
        :src="house.image"
        class="house-image rounded-lg"
        mode="aspectFill"
        :lazy-load="true"
      ></image>
      <!-- 视频标记 -->
      <view v-if="house.hasVideo" class="video-mark">
        <text class="i-carbon-play-filled text-white text-36rpx"></text>
      </view>
      <!-- VR标记 -->
      <view v-if="house.hasVR" class="vr-mark">
        <text class="text-white text-24rpx">VR</text>
      </view>
    </view>
    <view class="house-info flex-1 py-20rpx pr-20rpx overflow-hidden">
      <!-- 标题 -->
      <view class="title text-32rpx font-bold line-clamp-1">{{
        house.title || house.name
      }}</view>

      <!-- 房源详情 -->
      <view class="info text-28rpx color-grey mt-10rpx line-clamp-1">
        <template v-if="type === 'second' || type === 'rent'">
          <text>{{ house.layout || getLayoutText() }}</text>
          <text v-if="house.area"> | {{ house.area }}㎡</text>
          <text v-if="house.direction"> | {{ house.direction }}</text>
          <text v-if="house.floor"> | {{ house.floor }}</text>
          <text v-if="type === 'rent' && house.rentType">
            | {{ house.rentType }}</text
          >
        </template>
        <template v-else-if="type === 'new'">
          <text>{{ house.location || house.address }}</text>
        </template>
        <template v-else-if="type === 'commercial'">
          <text>{{ house.area }}㎡</text>
          <text v-if="house.type"> | {{ house.type }}</text>
          <text v-if="house.location"> | {{ house.location }}</text>
        </template>
        <template v-else>
          {{ house.info }}
        </template>
      </view>

      <!-- 标签 -->
      <view
        v-if="house.tags && house.tags.length"
        class="tags flex flex-wrap mt-16rpx"
      >
        <text
          v-for="(tag, index) in house.tags"
          :key="index"
          class="tag-item mr-10rpx mb-10rpx"
          >{{ tag }}</text
        >
      </view>

      <!-- 价格区域 -->
      <view class="price-line flex justify-between items-center mt-16rpx">
        <view>
          <text class="price text-34rpx font-bold" :class="priceColorClass">{{
            formattedPrice
          }}</text>
          <text
            v-if="house.unitPrice"
            class="unit-price text-24rpx color-grey ml-10rpx"
            >{{ formatUnitPrice(house.unitPrice) }}</text
          >
        </view>

        <!-- 额外信息：比如关注人数、发布时间等 -->
        <view v-if="house.extraInfo" class="extra-info text-24rpx color-grey">
          {{ house.extraInfo }}
        </view>
        <!-- 发布时间 -->
        <view v-else class="publish-time text-24rpx color-grey">
          {{ house.publishTime || "近期发布" }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

// 定义属性
const props = defineProps({
  // 房源信息
  house: {
    type: Object,
    required: true,
  },
  // 房源类型：second(二手房)、new(新房)、rent(租房)、commercial(商铺办公)
  type: {
    type: String,
    default: "second",
  },
});

// 控制点击效果
const isActive = ref(false);

// 价格颜色类
const priceColorClass = computed(() => {
  if (props.type === "rent") {
    return "color-orange";
  } else if (props.type === "commercial") {
    return "color-blue";
  } else {
    return "color-red";
  }
});

// 格式化价格
const formattedPrice = computed(() => {
  if (!props.house.price) return "";

  // 如果已经是格式化的价格字符串，直接返回
  if (typeof props.house.price === "string") {
    return props.house.price;
  }

  // 根据类型格式化价格
  switch (props.type) {
    case "second":
      return `${props.house.price}万`;
    case "new":
      return `${props.house.price}元/㎡起`;
    case "rent":
      return `${props.house.price}元/月`;
    case "commercial":
      return props.house.priceType === "sale"
        ? `${props.house.price}万`
        : `${props.house.price}元/月`;
    default:
      return `${props.house.price}`;
  }
});

// 格式化单价
const formatUnitPrice = (unitPrice: number | string) => {
  if (!unitPrice) return "";

  if (typeof unitPrice === "number") {
    return unitPrice.toLocaleString() + "元/㎡";
  }

  return unitPrice;
};

// 从已有属性获取户型文本
const getLayoutText = () => {
  const { rooms, halls } = props.house;
  if (rooms && halls) {
    return `${rooms}室${halls}厅`;
  }
  return "";
};

// 导航到详情页
const navigateToDetail = () => {
  // 将房源类型和ID作为参数传递到通用详情页
  uni.navigateTo({
    url: `/pages/house/detail?id=${props.house.id}&type=${props.type}`,
  });
};
</script>

<style lang="scss" scoped>
.house-item {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &.active-scale {
    transform: scale(0.98);
    box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.03);
  }
}

.house-image-container {
  position: relative;
  width: 220rpx;
  height: 160rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.house-image {
  width: 220rpx;
  height: 160rpx;
  transition: transform 0.3s ease;

  .house-item:active & {
    transform: scale(1.05);
  }
}

.new-tag {
  border-radius: 0 0 8rpx 0;
}

.video-mark {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}

.vr-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background-color: #f6f6f6;
  color: #666;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.color-red {
  color: #fa5741;
}

.color-orange {
  color: #ff8f00;
}

.color-blue {
  color: #1976d2;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.color-grey {
  color: #999;
}
</style>
