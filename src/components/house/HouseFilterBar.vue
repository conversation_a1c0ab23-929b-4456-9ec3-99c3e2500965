<template>
  <view class="house-filter-bar">
    <!-- 筛选条件区域 -->
    <view class="filter-bar bg-white">
      <scroll-view scroll-x class="filter-scroll-view">
        <view class="filter-tabs flex px-20rpx">
          <!-- 区域筛选 -->
          <view
            v-if="showArea"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'area' }"
            @tap="openFilter('area')"
          >
            <text class="mr-6rpx">{{ filters.area || areaText }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'area' }"
            ></text>
          </view>

          <!-- 租房方式 -->
          <view
            v-if="showRentType"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'rentType' }"
            @tap="openFilter('rentType')"
          >
            <text class="mr-6rpx">{{ filters.rentType || rentTypeText }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'rentType' }"
            ></text>
          </view>

          <!-- 商业地产类型 -->
          <view
            v-if="showPropertyType"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'propertyType' }"
            @tap="openFilter('propertyType')"
          >
            <text class="mr-6rpx">{{
              filters.propertyType || propertyTypeText
            }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'propertyType' }"
            ></text>
          </view>

          <!-- 价格筛选 -->
          <view
            v-if="showPrice"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'price' }"
            @tap="openFilter('price')"
          >
            <text class="mr-6rpx">{{ filters.price || priceText }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'price' }"
            ></text>
          </view>

          <!-- 户型筛选 -->
          <view
            v-if="showHouseType"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'houseType' }"
            @tap="openFilter('houseType')"
          >
            <text class="mr-6rpx">{{
              filters.houseType || houseTypeText
            }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'houseType' }"
            ></text>
          </view>

          <!-- 更多筛选 -->
          <view
            v-if="showMore"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'more' }"
            @tap="openFilter('more')"
          >
            <text class="mr-6rpx">更多</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'more' }"
            ></text>
          </view>

          <!-- 排序筛选 -->
          <view
            v-if="showSort"
            class="filter-tab-item py-20rpx px-16rpx flex items-center"
            :class="{ 'active-tab': activeFilter === 'sort' }"
            @tap="openFilter('sort')"
          >
            <text class="mr-6rpx">{{ filters.sort || sortText }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx transition-transform"
              :class="{ 'rotate-180': activeFilter === 'sort' }"
            ></text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-区域 -->
    <view
      v-if="activeFilter === 'area'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in areaOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ 'active-option': filters.area === item.label }"
            @tap.stop="selectFilter('area', item)"
          >
            <text class="option-text">{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-租房方式 -->
    <view
      v-if="activeFilter === 'rentType'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in rentTypeOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ 'active-option': filters.rentType === item.label }"
            @tap.stop="selectFilter('rentType', item)"
          >
            <text class="option-text">{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-商业地产类型 -->
    <view
      v-if="activeFilter === 'propertyType'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in propertyTypeOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ 'active-option': filters.propertyType === item.label }"
            @tap.stop="selectFilter('propertyType', item)"
          >
            <text class="option-text">{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-价格 -->
    <view
      v-if="activeFilter === 'price'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in priceOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ 'active-option': filters.price === item.label }"
            @tap.stop="selectFilter('price', item)"
          >
            <text class="option-text">{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-户型 -->
    <view
      v-if="activeFilter === 'houseType'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view class="flex flex-wrap">
            <view
              v-for="(item, index) in houseTypeOptions"
              :key="index"
              class="house-type-item py-16rpx px-30rpx m-10rpx rounded text-center"
              :class="{
                'active-option-item': filters.houseType === item.label,
              }"
              @tap.stop="selectFilter('houseType', item)"
            >
              <text class="option-text">{{ item.label }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板-排序 -->
    <view
      v-if="activeFilter === 'sort'"
      class="filter-panel bg-white"
      @tap="closeFilterIfClickOutside"
    >
      <scroll-view scroll-y class="panel-scroll">
        <view class="filter-options px-20rpx py-30rpx">
          <view
            v-for="(item, index) in sortOptions"
            :key="index"
            class="option-item py-20rpx"
            :class="{ 'active-option': filters.sort === item.label }"
            @tap.stop="selectFilter('sort', item)"
          >
            <text class="option-text">{{ item.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 遮罩层 -->
    <view v-if="activeFilter" class="filter-mask" @tap="closeFilter"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from "vue";

// 定义组件接受的属性
const props = defineProps({
  // 控制显示哪些筛选项
  showArea: { type: Boolean, default: true },
  showPrice: { type: Boolean, default: true },
  showHouseType: { type: Boolean, default: false },
  showRentType: { type: Boolean, default: false },
  showPropertyType: { type: Boolean, default: false },
  showMore: { type: Boolean, default: true },
  showSort: { type: Boolean, default: true },

  // 筛选项文本
  areaText: { type: String, default: "区域" },
  priceText: { type: String, default: "价格" },
  houseTypeText: { type: String, default: "户型" },
  rentTypeText: { type: String, default: "方式" },
  propertyTypeText: { type: String, default: "类型" },
  sortText: { type: String, default: "默认排序" },

  // 筛选数据选项
  areaOptions: {
    type: Array as PropType<any[]>,
    default: () => [
      { label: "不限", value: "" },
      { label: "朝阳区", value: "chaoyang" },
      { label: "海淀区", value: "haidian" },
      { label: "东城区", value: "dongcheng" },
      { label: "西城区", value: "xicheng" },
    ],
  },
  priceOptions: {
    type: Array as PropType<any[]>,
    default: () => [] as any, // 由父组件传入
  },
  houseTypeOptions: {
    type: Array as PropType<any[]>,
    default: () => [
      { label: "不限", value: "" },
      { label: "一室", value: "1" },
      { label: "二室", value: "2" },
      { label: "三室", value: "3" },
      { label: "四室", value: "4" },
      { label: "五室+", value: "5+" },
    ],
  },
  rentTypeOptions: {
    type: Array as PropType<any[]>,
    default: () => [
      { label: "不限", value: "" },
      { label: "整租", value: "entire" },
      { label: "合租", value: "shared" },
      { label: "公寓", value: "apartment" },
    ],
  },
  propertyTypeOptions: {
    type: Array as PropType<any[]>,
    default: () => [
      { label: "不限", value: "" },
      { label: "商铺", value: "shop" },
      { label: "写字楼", value: "office" },
      { label: "厂房", value: "factory" },
      { label: "仓库", value: "warehouse" },
    ],
  },
  sortOptions: {
    type: Array as PropType<any[]>,
    default: () => [
      { label: "默认排序", value: "default" },
      { label: "价格从低到高", value: "price_asc" },
      { label: "价格从高到低", value: "price_desc" },
      { label: "最新发布", value: "time_desc" },
    ],
  },

  // 初始筛选值
  initialFilters: {
    type: Object as PropType<any>,
    default: () => ({} as any),
  },
});

// 筛选项状态
const filters = reactive({
  area: props.initialFilters.area || "",
  price: props.initialFilters.price || "",
  houseType: props.initialFilters.houseType || "",
  rentType: props.initialFilters.rentType || "",
  propertyType: props.initialFilters.propertyType || "",
  sort: props.initialFilters.sort || "",
});

const emit = defineEmits(["filter-change"]);
const activeFilter = ref(""); // 当前激活的筛选项

// 打开筛选面板
const openFilter = (filterName: string) => {
  if (activeFilter.value === filterName) {
    activeFilter.value = "";
  } else {
    activeFilter.value = filterName;
  }
};

// 关闭筛选面板
const closeFilter = () => {
  activeFilter.value = "";
};

// 点击空白区域关闭筛选面板
const closeFilterIfClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target.classList.contains("filter-panel")) {
    closeFilter();
  }
};

// 选择筛选项
const selectFilter = (filterType: string, item: any) => {
  if (item.label === "不限") {
    filters[filterType] = "";
  } else {
    filters[filterType] = item.label;
  }

  // 通知父组件筛选条件变更
  emit("filter-change", { ...filters, [filterType]: item.value });
  closeFilter();
};

// 监听初始筛选值变化
watch(
  () => props.initialFilters,
  (newFilters) => {
    Object.keys(newFilters).forEach((key) => {
      if (key in filters) {
        filters[key] = newFilters[key];
      }
    });
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.house-filter-bar {
  position: relative;
}

.filter-bar {
  position: sticky;
  top: 0;
  z-index: 98;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-scroll-view {
  white-space: nowrap;
}

.filter-tabs {
  height: 88rpx;
  align-items: center;
}

.filter-tab-item {
  font-size: 28rpx;
  color: #666;
  margin-right: 40rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  position: relative;

  &.active-tab {
    color: $primary;
    font-weight: 600;
    background: linear-gradient(135deg, rgba($primary, 0.1) 0%, rgba($primary, 0.05) 100%);

    &::after {
      content: '';
      position: absolute;
      bottom: -2rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 24rpx;
      height: 4rpx;
      background: linear-gradient(135deg, $primary 0%, lighten($primary, 10%) 100%);
      border-radius: 2rpx;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.transition-transform {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

.filter-panel {
  position: absolute;
  top: 80rpx;
  left: 0;
  width: 100%;
  max-height: 600rpx;
  z-index: 99;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.panel-scroll {
  max-height: 600rpx;
}

.option-item {
  font-size: 28rpx;
  color: #333;

  &.active-option {
    color: $primary;
    font-weight: 500;
  }
}

.house-type-item {
  background-color: #f5f5f5;
  font-size: 28rpx;

  &.active-option-item {
    background-color: rgba($primary, 0.1);
    color: $primary;
    font-weight: 500;
  }
}

.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 97;
}
</style>
