<template>
  <view class="home-container">
    <!-- 顶部搜索区域 -->
    <view class="search-bar">
      <view class="location">
        <text>北京</text>
        <uni-icons type="bottom" size="14" color="#333"></uni-icons>
      </view>
      <view class="search-input">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <text class="placeholder">输入小区或商圈名开始找房</text>
      </view>
      <view class="map-btn cursor-pointer">
        <uni-icons type="location" size="22" color="#3B7FFF"></uni-icons>
      </view>
    </view>
    <!-- 滚动内容区域 -->
    <scroll-view
      scroll-y
      enable-flex
      class="scroll-container"
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
    >
      <!-- 轮播图 - 调整为黄金比例 -->
      <swiper
        class="banner"
        circular
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
      >
        <swiper-item
          v-for="(item, index) in banners"
          :key="index"
          class="cursor-pointer"
        >
          <image :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>

      <!-- 房源类型导航 - 修复上下布局 -->
      <view class="house-types">
        <view
          v-for="(item, index) in houseTypes"
          :key="index"
          class="type-item"
        >
          <navigator :url="item.url" hover-class="none" class="type-content">
            <view class="icon-wrapper">
              <i class="icon" :class="item.icon"></i>
            </view>
            <text class="type-name">{{ item.name }}</text>
          </navigator>
        </view>
      </view>

      <!-- 筛选条件栏 - 样式与图片一致 -->
      <view class="tab-bar">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTabIndex === index }"
          @click="switchTab(index)"
        >
          <text>{{ tab }}</text>
        </view>
      </view>

      <!-- 推荐房源列表 -->
      <view class="house-list">
        <HouseCard
          v-for="(house, index) in houseList"
          :key="index"
          :house="house"
          :type="getHouseType(house.type)"
        />
        <view v-if="loading" class="loading">
          <uni-icons type="spinner-cycle" size="20" color="#3B7FFF"></uni-icons>
          <text>加载中...</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import HouseCard from "@/components/house/HouseCard.vue";
// 轮播图数据
const banners = reactive([
  {
    image:
      "https://readdy.ai/api/search-image?query=modern%20residential%20building%20complex%20with%20beautiful%20landscaping%2C%20high-quality%20real%20estate%20photography%2C%20luxury%20apartments%20with%20glass%20facades%2C%20sunny%20day%2C%20blue%20sky%2C%20professional%20real%20estate%20marketing%20image%2C%20wide%20angle%20view%2C%20architectural%20photography%2C%20high%20resolution%2C%20property%20showcase&width=750&height=350&seq=1&orientation=landscape",
  },
  {
    image:
      "https://readdy.ai/api/search-image?query=interior%20of%20luxury%20apartment%2C%20open%20concept%20living%20room%20with%20floor%20to%20ceiling%20windows%2C%20modern%20furniture%2C%20natural%20lighting%2C%20hardwood%20floors%2C%20minimalist%20design%2C%20real%20estate%20photography%2C%20high-end%20property%2C%20spacious%20and%20bright%2C%20professional%20staging&width=750&height=350&seq=2&orientation=landscape",
  },
  {
    image:
      "https://readdy.ai/api/search-image?query=new%20residential%20development%20with%20swimming%20pool%20and%20garden%20areas%2C%20aerial%20view%20of%20modern%20housing%20complex%2C%20contemporary%20architecture%2C%20community%20living%20spaces%2C%20real%20estate%20development%20showcase%2C%20professional%20property%20photography&width=750&height=350&seq=3&orientation=landscape",
  },
]);

// 房源类型数据 - 使用iconify图标
const houseTypes = reactive([
  {
    name: "二手房",
    icon: "i-carbon-home",
    url: "/pages/house/secondHouse/list",
  },
  {
    name: "新房",
    icon: "i-carbon-building",
    url: "pages/house/newHouse/list",
  },
  {
    name: "租房",
    icon: "i-carbon-key",
    url: "pages/house/rent/list",
  },
  {
    name: "商铺",
    icon: "i-carbon-store",
    url: "pages/house/commercial/list?type=shop",
  },
  {
    name: "写字楼",
    icon: "i-carbon-office",
    url: "pages/house/commercial/list?type=office",
  },
]);

// 标签页数据 - 与图片一致
const tabs = reactive(["推荐", "二手房", "新房", "租房", "装修"]);
const activeTabIndex = ref(0);

// 切换标签页
const switchTab = (index: number) => {
  activeTabIndex.value = index;
};

// 吸顶逻辑
const filterBarTop = ref(0);
onPageScroll(({ scrollTop }) => {
  if (scrollTop >= 380) {
    filterBarTop.value = 0;
  } else {
    filterBarTop.value = -1;
  }
});

// 房源列表数据 - 增加房源类型标识和详细信息
const houseList = reactive([
  {
    id: "1",
    title: "金茂府 南北通透三居室 精装修 拎包入住",
    community: "金茂府",
    layout: "3室2厅2卫",
    area: "120",
    floor: "中楼层",
    direction: "南北",
    location: "朝阳区 · 金茂府",
    tags: ["南北通透", "精装修", "近地铁"],
    price: "890",
    unitPrice: "74167元/㎡",
    type: "二手",
    isHot: true,
    image:
      "https://readdy.ai/api/search-image?query=modern%20apartment%20interior%20with%20large%20windows%2C%20bright%20living%20room%20with%20contemporary%20furniture%2C%20hardwood%20floors%2C%20minimalist%20design%2C%20natural%20lighting%2C%20spacious%20and%20clean%2C%20real%20estate%20photography%20style%2C%20professional%20property%20image&width=240&height=180&seq=8&orientation=landscape",
  },
  {
    id: "2",
    title: "整租2居 · 香山南营66号",
    community: "香山南营66号",
    layout: "2室1厅1卫",
    area: "70",
    floor: "高楼层",
    direction: "南北",
    rentType: "整租",
    location: "西山",
    tags: ["南北", "高楼层", "步梯"],
    price: "4600",
    paymentMethod: "押一付一",
    utilities: "民水民电",
    extraInfo: "今日新上",
    type: "整租",
    hasVR: true,
    image:
      "https://readdy.ai/api/search-image?query=luxury%20condominium%20with%20city%20view%2C%20modern%20high-rise%20apartment%2C%20large%20balcony%2C%20floor%20to%20ceiling%20windows%2C%20contemporary%20design%2C%20evening%20lighting%2C%20urban%20living%2C%20real%20estate%20photography&width=240&height=180&seq=9&orientation=landscape",
  },
  {
    id: "3",
    title: "保利熙悦 三室两厅 低楼层 采光好 交通便利",
    community: "保利熙悦",
    layout: "3室2厅",
    area: "95",
    floor: "低楼层",
    direction: "南",
    location: "丰台区 · 保利熙悦",
    tags: ["低楼层", "采光好", "交通便利"],
    price: "720",
    unitPrice: "75789元/㎡",
    type: "新房",
    image:
      "https://readdy.ai/api/search-image?query=bright%20apartment%20interior%20with%20large%20windows%2C%20modern%20living%20room%2C%20natural%20light%2C%20white%20walls%2C%20wooden%20floors%2C%20contemporary%20furniture%2C%20clean%20and%20spacious%2C%20real%20estate%20photography%20style&width=240&height=180&seq=10&orientation=landscape",
  },
  {
    id: "4",
    title: "临街商铺 适合餐饮零售",
    community: "商业街",
    layout: "一层",
    area: "50",
    floor: "1层",
    location: "朝阳区 · 商业街",
    tags: ["临街", "人流量大", "适合餐饮"],
    price: "8000",
    priceType: "rent",
    type: "商铺",
    image:
      "https://readdy.ai/api/search-image?query=small%20studio%20apartment%20with%20modern%20design%2C%20compact%20living%20space%2C%20efficient%20layout%2C%20contemporary%20furniture%2C%20bright%20colors%2C%20clean%20lines%2C%20real%20estate%20photography%20style&width=240&height=180&seq=11&orientation=landscape",
  },
]);

// 加载状态
const loading = ref(false);
const isRefreshing = ref(false);

// 加载更多
const loadMore = () => {
  if (loading.value) return;
  loading.value = true;
  // 模拟加载更多数据
  const newHouses = [
    {
      id: "5",
      title: "首开华润城 精装三居 南向 采光好 近地铁",
      community: "首开华润城",
      layout: "3室2厅1卫",
      area: "110",
      floor: "中楼层",
      direction: "南向",
      location: "大兴区 · 首开华润城",
      tags: ["精装修", "南向", "近地铁"],
      price: "680",
      unitPrice: "61818元/㎡",
      type: "二手",
      isHot: false,
      discount: "降价14万",
      image:
        "https://readdy.ai/api/search-image?query=modern%20three%20bedroom%20apartment%20interior%2C%20bright%20living%20space%2C%20contemporary%20design%2C%20large%20windows%2C%20natural%20light%2C%20clean%20aesthetic%2C%20real%20estate%20photography%20style&width=240&height=180&seq=14&orientation=landscape",
    },
    {
      id: "6",
      title: "万科翡翠公园 全新毛坯 大三居 视野开阔",
      community: "万科翡翠公园",
      layout: "3室2厅2卫",
      area: "140",
      floor: "高楼层",
      direction: "东南",
      location: "顺义区 · 万科翡翠公园",
      tags: ["毛坯房", "大三居", "视野开阔"],
      price: "950",
      unitPrice: "67857元/㎡",
      type: "新房",
      isHot: true,
      image:
        "https://readdy.ai/api/search-image?query=empty%20apartment%20interior%20with%20large%20windows%2C%20unfurnished%20space%2C%20white%20walls%2C%20concrete%20floors%2C%20spacious%20rooms%2C%20bright%20natural%20lighting%2C%20real%20estate%20photography%20style&width=240&height=180&seq=15&orientation=landscape",
    },
  ];
  houseList.push(...newHouses);
  loading.value = false;
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    // 模拟刷新数据
    isRefreshing.value = false;
  }, 1000);
};

// 获取房源类型
const getHouseType = (type: string) => {
  const typeMap: Record<string, string> = {
    二手: "second",
    新房: "new",
    整租: "rent",
    合租: "rent",
    商铺: "commercial",
    写字楼: "commercial",
  };
  return typeMap[type] || "second";
};
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f6f7;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.location {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.location text {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-right: 4rpx;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #f5f6f7;
  border-radius: 35rpx;
  padding: 0 20rpx;
}

.search-input .placeholder {
  margin-left: 10rpx;
  font-size: 13px;
  color: #999999;
}

.map-btn {
  margin-left: 20rpx;
  padding: 10rpx;
  flex-shrink: 0;
}

.scroll-container {
  flex: 1;
  overflow: auto;
}

/* 轮播图 - 调整为黄金比例 */
.banner {
  width: 98%;
  height: 280rpx; /* 调整为黄金比例的高度 750/1.618 ≈ 464，但为了更节省空间，调整为280rpx */
  margin: 20rpx;
}

.banner image {
  width: 100%;
  height: 100%;
  border-radius: 0 0 16rpx 16rpx;
}

/* 房源类型导航 - 修复上下布局 */
.house-types {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20rpx 10rpx;
  background-color: #ffffff;
  margin-bottom: 16rpx;
}

.type-item {
  width: 20%;
  margin-bottom: 20rpx;
}

.type-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.icon {
  font-size: 40rpx;
  color: #ff6d00;
}

.type-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

/* 标签页 - 与图片样式一致 */
.tab-bar {
  display: flex;
  gap: 20rpx;
  margin: 28rpx 0;
  position: sticky;
  top: 0;
  z-index: 99;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  padding: 0 20rpx;
}

.tab-item text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 400;
}

.tab-item.active text {
  color: #333333;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #333333;
  border-radius: 2rpx;
}

/* 房源列表 - 优化样式 */
.house-list {
  padding: 16rpx 20rpx;
}

.house-card {
  display: flex;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.house-image {
  width: 220rpx;
  height: 170rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.house-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 170rpx;
}

.house-title-row {
  display: flex;
  align-items: center;
}

.house-type-tag {
  font-size: 22rpx;
  color: #ffffff;
  background-color: #ff6d00;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.house-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: 600;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.house-location {
  font-size: 26rpx;
  color: #666666;
  margin-top: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 6rpx;
}

.tag {
  font-size: 22rpx;
  color: #3b7fff;
  background-color: rgba(59, 127, 255, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

.house-price-info {
  display: flex;
  align-items: baseline;
  margin-top: 6rpx;
}

.house-price {
  font-size: 32rpx;
  color: #ff5a5f;
  font-weight: 600;
}

.house-unit {
  font-size: 24rpx;
  color: #999999;
  margin-left: 6rpx;
}

.house-discount {
  font-size: 22rpx;
  color: #ff5a5f;
  margin-left: 16rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

.loading text {
  margin-left: 10rpx;
}
</style>
